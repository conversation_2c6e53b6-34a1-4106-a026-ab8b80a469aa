"""
This module defines functions to create initial messages prompts for generating
questions based on resumes, job descriptions, and roles.
"""

from datetime import datetime
from api.database.prompt_db import fetch_latest_prompt

def get_resume_initial_message(text,role=None):
    """
    Creates an initial message for generating questions based on the provided
    resume text and optional role.
    """
    # print("role",role)
    if role:
        role_string = f"\nRole: {role}"
    else:
        role_string = ""
    current_date = datetime.today().strftime('%Y-%m-%d')
    db_prompt = fetch_latest_prompt("resume")
    db_prompt = db_prompt.format(role_string = role_string, current_date = current_date)
    # print("DB prompt for Resume: ", db_prompt)
    messages = [
    {"role": "system", "content": f""" {db_prompt}
     
Current date: {current_date} 
Response Generation Guidelines:
Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering. 
Dont output anything other than JSON not even markdown.
"""},
    ]
    messages.append({"role": "user", "content": "Parsed text from resume: " + text})
    messages.append({"role": "user", "content": "\nQuestions:"})
    return messages

def  get_jd_initial_message(text):
    """
    Creates an initial message for generating questions based on the provided
    job description text.
    """
    db_prompt = fetch_latest_prompt("jd")
    # print("DB Prompt for JD: ", db_prompt)
    messages = [
    {"role": "system", "content": f""" {db_prompt}
     
Response Generation Guidelines:
Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering. Dont output anything other than JSON not even markdown.  

"""}
    ]
    messages.append({"role": "user", "content": "Parsed text from job description: " + text})
    messages.append({"role": "user", "content": "\nQuestions:"})
    return messages 

def get_role_initial_message(role, experience_level=None): 
    """
    Creates an initial message for generating questions based on the provided
    role and optional experience level.
    """
    # logger.info("Generating questions based on role and experience level", role, experience_level)
    experience_note = f" for an {experience_level}" if experience_level else ""
    db_prompt = fetch_latest_prompt("job_title")
    db_prompt = db_prompt.format(experience_note=experience_note, role=role)

    # print("DB Prompt for Job title: ", db_prompt)
    messages = [
        {"role": "system", "content": f""" {db_prompt}
         
Response Generation Guidelines:
Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering. Dont output anything other than JSON not even markdown.
"""}
    ]
    messages.append({"role": "user", "content": "\nJob title or Role: " + role + experience_note})
    messages.append({"role": "user", "content": "\nQuestions:\n"})
    return messages

# def get_role_initial_message(role):
#     messages = [
#     {"role": "system", "content": f"""As an AI Hiring specialist, your task is to analyze the provided job title or role and generate a list of relevant interview questions. Take into account the following notes to create insightful inquiries:
     
# 1. Prepare list of skills required for the role : {role}
# 2. Generate a list of questions that will help you assess the candidate's proficiency in each of these skills.
# 3. Ensure that the questions are relevant, insightful, and appropriate, and avoid asking any questions that may be considered inappropriate.
# 4. Your goal is to gain a comprehensive understanding of the candidate's capabilities and determine their alignment with the job requirements.
# 5. Generate a set of follow-up interview questions that will help you make an informed hiring decision.
# You can ask questions such as:
# 1. What is your experience with [specific requirement]?
# 2. How has your previous experience prepared you for this role?
# 3. How will your expertise in [specific skill] contribute to your success in this role?
# 4. How did you handle [a specific task related to {role}] ?
# 5. Could you describe your typical approach to utilizing this skill?

# Response Generation Guidelines:
# Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering. Dont output anything other than JSON not even markdown.
# """}
#     ]
#     messages.append({"role": "user", "content": "\nJob title or Role: " + role})
#     return messages 

def get_feedback_initial_message():
    """
    Creates an initial message for generating feedback based on the provided
    question and answer.
    """

    prompt = """You are an expert interview coach. Your job is to review interview responses and provide two things:
    1. **Suggestions**: Give clear, specific, and actionable feedback on how the response can be improved. Focus on clarity, relevance, tone, completeness, and professionalism.
    2. **Proposed Answer**: Rewrite the user's response to improve it while preserving their tone, intent, and style. Do not generate a generic or overly polished answer that doesn't feel like the original. Instead, subtly improve structure, clarity, and language while keeping the user's voice.
    """ 
    return prompt
