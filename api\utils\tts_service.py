"""
TTS (Text-to-Speech) service using ElevenLabs API.
Handles audio generation, file management, and job tracking.
"""

import os
import uuid
import asyncio
from datetime import datetime
from typing import List, Tuple, Optional
from pathlib import Path
import aiofiles
from elevenlabs import ElevenLabs, VoiceSettings
from api.common.api_logger import api_logger as logger
from api.common.pydantic_models import AudioFile, VoiceMapping
from dotenv import load_dotenv

load_dotenv()

class TTSService:
    """Service class for handling TTS operations with ElevenLabs."""

    def __init__(self):
        """Initialize the TTS service with ElevenLabs client."""
        self.api_key = os.getenv("elevenlabs_api_key")
        if not self.api_key:
            raise ValueError("ElevenLabs API key not found in environment variables")

        self.client = ElevenLabs(api_key=self.api_key)
        self.audio_dir = Path("audio_files")
        self.audio_dir.mkdir(exist_ok=True)

        # Voice settings for consistent quality
        self.voice_settings = VoiceSettings(
            stability=0.71,
            similarity_boost=0.5,
            style=0.0,
            use_speaker_boost=True
        )

    def generate_job_id(self) -> str:
        """Generate a unique job ID for tracking audio generation."""
        return f"audio_job_{uuid.uuid4().hex[:12]}"

    async def generate_audio_file(
        self,
        text: str,
        voice_id: str,
        model: str = "eleven_flash_v2_5",
        file_path: str = None
    ) -> Tuple[bool, str, Optional[float]]:
        """
        Generate audio file for a single text using ElevenLabs API.

        Args:
            text: Text to convert to speech
            voice_id: ElevenLabs voice ID
            model: ElevenLabs model to use
            file_path: Optional custom file path

        Returns:
            Tuple of (success, file_path, duration_seconds)
        """
        try:
            if not file_path:
                file_path = self.audio_dir / f"{uuid.uuid4().hex}.mp3"

            logger.info(f"Generating audio for text: {text[:50]}...")

            # Generate audio using ElevenLabs
            audio_generator = self.client.generate(
                text=text,
                voice=voice_id,
                model=model,
                voice_settings=self.voice_settings
            )

            # Save audio to file
            audio_data = b"".join(audio_generator)

            async with aiofiles.open(file_path, "wb") as f:
                await f.write(audio_data)

            # Calculate approximate duration (rough estimate: 150 words per minute)
            word_count = len(text.split())
            duration_seconds = (word_count / 150) * 60

            logger.info(f"Audio generated successfully: {file_path}")
            return True, str(file_path), duration_seconds

        except Exception as e:
            logger.error(f"Error generating audio: {str(e)}")
            return False, str(e), None

    async def generate_multiple_audio_files(
        self,
        questions: List[str],
        voice_id: str,
        job_id: str,
        model: str = "eleven_flash_v2_5"
    ) -> List[AudioFile]:
        """
        Generate audio files for multiple questions.

        Args:
            questions: List of questions to convert to audio
            voice_id: ElevenLabs voice ID
            job_id: Unique job identifier
            model: ElevenLabs model to use

        Returns:
            List of AudioFile objects
        """
        audio_files = []
        job_dir = self.audio_dir / job_id
        job_dir.mkdir(exist_ok=True)

        logger.info(f"Starting audio generation for {len(questions)} questions")

        for index, question in enumerate(questions):
            try:
                # Generate unique filename for each question
                filename = f"question_{index + 1}_{uuid.uuid4().hex[:8]}.mp3"
                file_path = job_dir / filename

                # Generate audio
                success, result_path, duration = await self.generate_audio_file(
                    text=question,
                    voice_id=voice_id,
                    model=model,
                    file_path=str(file_path)
                )

                if success:
                    # Create download URL (relative to API base URL)
                    file_url = f"/audio/download/{job_id}/{filename}"

                    audio_file = AudioFile(
                        question_index=index,
                        question_text=question,
                        file_path=str(file_path),
                        file_url=file_url,
                        duration_seconds=duration
                    )
                    audio_files.append(audio_file)
                    logger.info(f"Generated audio for question {index + 1}/{len(questions)}")
                else:
                    logger.error(f"Failed to generate audio for question {index + 1}: {result_path}")

            except Exception as e:
                logger.error(f"Error processing question {index + 1}: {str(e)}")
                continue

        logger.info(f"Audio generation completed. {len(audio_files)}/{len(questions)} files generated")
        return audio_files

    def get_file_path(self, job_id: str, filename: str) -> Optional[Path]:
        """Get the full file path for a specific audio file."""
        file_path = self.audio_dir / job_id / filename
        return file_path if file_path.exists() else None

    def cleanup_job_files(self, job_id: str) -> bool:
        """Clean up all files for a specific job."""
        try:
            job_dir = self.audio_dir / job_id
            if job_dir.exists():
                for file_path in job_dir.iterdir():
                    file_path.unlink()
                job_dir.rmdir()
                logger.info(f"Cleaned up files for job: {job_id}")
                return True
        except Exception as e:
            logger.error(f"Error cleaning up job {job_id}: {str(e)}")
        return False

    def get_available_voices(self) -> dict:
        """Get mapping of available voices with their descriptions."""
        return {
            VoiceMapping.RACHEL.value: "Rachel - Asian Women",
            VoiceMapping.CLARITY.value: "Clarity Speaks - Black Women",
            VoiceMapping.BRIAN.value: "Brian - White Male",
            VoiceMapping.DONTAVIOUS.value: "Dontavious Breighton - Black Male (Casual)",
            VoiceMapping.GEORGE.value: "George - Black Male (Formal)"
        }

    def map_image_to_voice(self, image_id: str) -> str:
        """Map interviewer image ID to ElevenLabs voice ID."""
        image_to_voice_mapping = {
            "asian_women": VoiceMapping.RACHEL.value,           # 21m00Tcm4TlvDq8ikWAM
            "black_women": VoiceMapping.CLARITY.value,          # zbj5pYu7PWmTR3zNpMct
            "white_male": VoiceMapping.BRIAN.value,             # nPczCjzI2devNBz1zQrb
            "black_male_casual": VoiceMapping.DONTAVIOUS.value, # gUot1J0p7f1TAO8rUA9w
            "black_male_formal": VoiceMapping.GEORGE.value      # JBFqnCBsd6RMkjVDRZzb
        }

        voice_id = image_to_voice_mapping.get(image_id.lower())
        if not voice_id:
            # Default to Rachel if image_id not found
            logger.warning(f"Unknown image_id: {image_id}, defaulting to Rachel")
            voice_id = VoiceMapping.RACHEL.value

        return voice_id

    def get_image_voice_mapping(self) -> dict:
        """Get complete mapping of image IDs to voice information."""
        return {
            "asian_women": {
                "voice_id": VoiceMapping.RACHEL.value,
                "voice_name": "Rachel",
                "description": "Asian Women - Professional, clear voice"
            },
            "black_women": {
                "voice_id": VoiceMapping.CLARITY.value,
                "voice_name": "Clarity Speaks",
                "description": "Black Women - Confident, articulate voice"
            },
            "white_male": {
                "voice_id": VoiceMapping.BRIAN.value,
                "voice_name": "Brian",
                "description": "White Male - Authoritative, professional voice"
            },
            "black_male_casual": {
                "voice_id": VoiceMapping.DONTAVIOUS.value,
                "voice_name": "Dontavious Breighton",
                "description": "Black Male (Casual) - Friendly, approachable voice"
            },
            "black_male_formal": {
                "voice_id": VoiceMapping.GEORGE.value,
                "voice_name": "George",
                "description": "Black Male (Formal) - Business-like, formal voice"
            }
        }

# Global TTS service instance
tts_service = TTSService()
