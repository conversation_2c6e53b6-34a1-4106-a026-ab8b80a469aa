"""
This module handles interview answer feedback generation and management.
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import List
from dotenv import load_dotenv
from api.utils.qa_model import generate_feedback_response
from api.database.prompt_db import fetch_latest_prompt
from api.database.feedback_db import (
    load_feedback_examples,
    add_feedback_example as db_add_feedback_example,
    delete_feedback_example as db_delete_feedback_example,
    export_feedback_examples_to_csv,
    import_feedback_examples_from_csv
)
from api.common.api_logger import api_logger as logger
from api.common.dependencies import get_api_key
from fastapi import Depends
from api.common.pydantic_models import (
    Feedback,
    FeedbackRequest,
    FeedbackExample,
    FeedbackExampleCreate,
    ApiResponse
)
import io

load_dotenv(override=True)
router = APIRouter(
    prefix="/feedback",
    tags=["Feedback"]
)

# No need to check if the table exists - it's already created in MySQL

@router.post("/generate-feedback", response_model=Feedback)
def generate_feedback(request: FeedbackRequest, token: bool = Depends(get_api_key)) -> Feedback:
    """Generate feedback for an interview answer"""
    try:
        logger.info(f"Generating feedback for question: {request.question}")
        prompt = fetch_latest_prompt("feedback")  # Get prompt from prompt management
        messages = [
            {"role": "system", "content": prompt},
            {
                "role": "user",
                "content": f"Question: {request.question}\nAnswer: {request.answer}"
            }
        ]

        # generate_response returns a tuple (response_dict, token_usage)
        response_tuple = generate_feedback_response(messages, request.model)
        if not response_tuple or not isinstance(response_tuple, tuple):
            raise ValueError("Invalid response format from model")

        response_dict = response_tuple[0]
        if not response_dict or "suggestions" not in response_dict or "proposed_answer" not in response_dict:
            raise ValueError("Response missing required fields")

        # Convert the response format to Feedback model
        feedback = Feedback(
            suggestions=response_dict["suggestions"],
            proposed_answer=response_dict["proposed_answer"]
        )

        return feedback

    except Exception as e:
        logger.error(f"Error generating feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list-feedback-examples", response_model=List[FeedbackExample])
def get_feedback_examples() -> List[FeedbackExample]:
    """Get all feedback examples"""
    return load_feedback_examples()

@router.post("/add-feedback-examples", response_model=ApiResponse)
def add_feedback_example(example: FeedbackExampleCreate) -> ApiResponse:
    """Add a new feedback example"""
    # Convert FeedbackExampleCreate to FeedbackExample with a temporary empty ID
    feedback_example = FeedbackExample(
        id="",  # This will be replaced in db_add_feedback_example
        question=example.question,
        answer=example.answer,
        suggestions=example.suggestions,
        proposed_answer=example.proposed_answer
    )

    success, added_example = db_add_feedback_example(feedback_example)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to add feedback example"
        )
    return ApiResponse(message="Feedback example added successfully", id=added_example.id)

@router.delete("/delete-feedback-examples", response_model=ApiResponse)
def delete_feedback_example(id: str) -> ApiResponse:
    """Delete a feedback example by ID"""
    success = db_delete_feedback_example(id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail="Feedback example not found"
        )

    return ApiResponse(message="Feedback example deleted successfully", id=id)

@router.get("/list-feedback-examples-csv")
def get_feedback_examples_csv():
    """Download all feedback examples as a CSV file"""
    try:
        success, csv_data = export_feedback_examples_to_csv()
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to export feedback examples to CSV"
            )

        # Create a response with the CSV file
        return StreamingResponse(
            iter([csv_data.getvalue()]),
            media_type="text/csv",
            headers={
                "Content-Disposition": "attachment; filename=feedback_examples.csv"
            }
        )
    except Exception as e:
        logger.error(f"Error exporting feedback examples to CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/add-feedback-examples-csv", response_model=ApiResponse)
async def add_feedback_examples_csv(
    file: UploadFile = File(...)
) -> ApiResponse:
    """
    Upload feedback examples from a CSV file. This will replace all existing examples.

    Required CSV columns:
    - question: The interview question
    - answer: The original answer provided by the candidate
    - suggestions: Improvement suggestions for the answer
    - proposed_answer: An improved version of the original answer

    Note: All existing feedback examples will be replaced with the ones from the uploaded CSV.
    """
    try:
        # Check file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=400,
                detail="Only CSV files are accepted"
            )

        # Read file content
        contents = await file.read()
        csv_file = io.BytesIO(contents)

        # Import feedback examples
        success, count = import_feedback_examples_from_csv(csv_file)
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to import feedback examples from CSV"
            )

        return ApiResponse(
            message=f"Successfully imported {count} feedback examples from CSV. All previous examples have been replaced.",
            id=None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error importing feedback examples from CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

