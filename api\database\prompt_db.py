"""
This module provides functions to interact with the prompt database.
It includes functionalities to fetch the latest prompt for a given request type
and to update prompts in the database.
"""

from api.common.api_logger import api_logger as logger
from api.database.connection_db import get_connection

def fetch_latest_prompt(request_type: str):
    """
    Fetches the latest prompt for a given request type.

    Parameters:
    - request_type (str): The type of request.

    Returns:
    - str: The latest prompt for the specified request type.
    """
    try:
        logger.info(f"Fetching latest prompt for request_type: {request_type}")
        connection = get_connection(is_ca=False)
        with connection.cursor() as cursor:
            query = "SELECT prompt FROM prompts WHERE request_type = %s ORDER BY created_at DESC LIMIT 1"
            cursor.execute(query, (request_type,))
            result = cursor.fetchone()
            return result["prompt"] if result else None
    except Exception as e:
        logger.error(f"An error occurred while fetching prompt: {e}")
        return None
    finally:
        connection.close()

def update_prompt_in_db(request_type: str, prompt: str):
    """
    Updates the prompt for a specified request type.

    Parameters:
    - request_type (str): The type of request.
    - prompt (str): The new prompt to set.

    Returns:
    - bool: True if the update was successful, False otherwise.
    """
    try:
        logger.info(f"Updating prompt for request_type: {request_type}")
        connection = get_connection(is_ca=False)
        with connection.cursor() as cursor:
            query = "UPDATE prompts SET prompt = %s WHERE request_type = %s"
            cursor.execute(query, (prompt, request_type))
            connection.commit()
            return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"An error occurred while updating prompt: {e}")
        return False
    finally:
        connection.close()