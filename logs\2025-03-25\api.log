2025-03-25 00:46:43,336 - api_logger - INFO - Request received for Job Title with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-25 00:46:45,122 - api_logger - INFO - Job Title Request with Session ID: new-test6 Successfully received and added to background task
2025-03-25 00:46:45,124 - api_logger - INFO - Starting background response generation for session_id: new-test6, job_title: data Scientist
2025-03-25 00:46:45,124 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: job_title
2025-03-25 00:46:45,382 - api_logger - INFO - Updating job experience for session_id: new-test6
2025-03-25 00:46:46,213 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: job_title, status: Inprogress
2025-03-25 00:46:46,833 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-25 00:46:48,382 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:46:55,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:46:55,056 - api_logger - INFO - Response received successfully.
2025-03-25 00:46:55,056 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test6
2025-03-25 00:46:55,056 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: job_title
2025-03-25 00:46:55,672 - api_logger - INFO - Inserted initial status for session_id: new-test6
2025-03-25 00:46:55,672 - api_logger - INFO - Updating status for session_id: new-test6, request_type: job_title, status: complete
2025-03-25 00:46:56,316 - api_logger - INFO - Status updated successfully.
2025-03-25 00:46:56,316 - api_logger - INFO - Response generated for session_id: new-test6, response_time: 9ms
2025-03-25 00:47:02,925 - api_logger - INFO - Request received for Job Title with Session ID: new-test7 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-25 00:47:04,661 - api_logger - INFO - Job Title Request with Session ID: new-test7 Successfully received and added to background task
2025-03-25 00:47:04,662 - api_logger - INFO - Starting background response generation for session_id: new-test7, job_title: data Scientist
2025-03-25 00:47:04,662 - api_logger - INFO - Checking if session does not exist for session_id: new-test7, request_type: job_title
2025-03-25 00:47:04,912 - api_logger - INFO - No previous session id
2025-03-25 00:47:04,912 - api_logger - INFO - Inserting status for session_id: new-test7, request_type: job_title, status: Inprogress
2025-03-25 00:47:05,413 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-25 00:47:06,872 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:47:14,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:47:14,843 - api_logger - INFO - Response received successfully.
2025-03-25 00:47:14,843 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test7
2025-03-25 00:47:14,843 - api_logger - INFO - Inserting record for session_id: new-test7, request_type: job_title
2025-03-25 00:47:15,353 - api_logger - INFO - Inserted initial status for session_id: new-test7
2025-03-25 00:47:15,357 - api_logger - INFO - Updating status for session_id: new-test7, request_type: job_title, status: complete
2025-03-25 00:47:15,846 - api_logger - INFO - Status updated successfully.
2025-03-25 00:47:15,847 - api_logger - INFO - Response generated for session_id: new-test7, response_time: 10ms
2025-03-25 00:47:41,404 - api_logger - INFO - Request received for JD with Session ID: new-test7 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:47:43,193 - api_logger - INFO - Getting file text for: temp\uploaded_ds_jd.pdf
2025-03-25 00:47:43,210 - api_logger - INFO - Processing PDF file.
2025-03-25 00:47:43,479 - api_logger - INFO - File text extraction completed.
2025-03-25 00:47:43,479 - api_logger - INFO - JD Request with Session ID: new-test7 Successfully received and added to background task
2025-03-25 00:47:43,495 - api_logger - INFO - Processing JD upload for session_id: new-test7
2025-03-25 00:47:43,495 - api_logger - INFO - Checking if session does not exist for session_id: new-test7, request_type: jd
2025-03-25 00:47:43,812 - api_logger - INFO - No previous session id
2025-03-25 00:47:43,812 - api_logger - INFO - Inserting status for session_id: new-test7, request_type: jd, status: Inprogress
2025-03-25 00:47:44,477 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-25 00:47:46,003 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:47:50,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:47:50,102 - api_logger - INFO - Response received successfully.
2025-03-25 00:47:50,102 - api_logger - INFO - Inserting record for session_id: new-test7, request_type: jd
2025-03-25 00:47:50,734 - api_logger - INFO - JD response generated for session_id: new-test7, response_time: 6.606705665588379s
2025-03-25 00:47:50,734 - api_logger - INFO - Updating status for session_id: new-test7, request_type: jd, status: complete
2025-03-25 00:47:51,272 - api_logger - INFO - Status updated successfully.
2025-03-25 00:47:58,769 - api_logger - INFO - Request received for Resume with Session ID: new-test7 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:48:00,445 - api_logger - INFO - Resume Request with Session ID: new-test7 Successfully received and added to background task
2025-03-25 00:48:00,448 - api_logger - INFO - Processing resume for session_id: new-test7
2025-03-25 00:48:00,448 - api_logger - INFO - Checking if session does not exist for session_id: new-test7, request_type: resume
2025-03-25 00:48:00,713 - api_logger - INFO - No previous session id found for session_id: new-test7
2025-03-25 00:48:00,713 - api_logger - INFO - Inserting status for session_id: new-test7, request_type: resume, status: Inprogress
2025-03-25 00:48:01,242 - api_logger - INFO - Getting file text for: temp\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-25 00:48:01,252 - api_logger - INFO - Processing PDF file.
2025-03-25 00:48:01,307 - api_logger - INFO - File text extraction completed.
2025-03-25 00:48:01,311 - api_logger - INFO - Successfully extracted resume text for session_id: new-test7
2025-03-25 00:48:01,311 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-25 00:48:03,092 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:48:09,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:48:09,559 - api_logger - INFO - Response received successfully.
2025-03-25 00:48:09,560 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test7
2025-03-25 00:48:09,560 - api_logger - INFO - Inserting record for session_id: new-test7, request_type: resume
2025-03-25 00:48:10,086 - api_logger - INFO - Updating status for session_id: new-test7, request_type: resume, status: complete
2025-03-25 00:48:10,606 - api_logger - INFO - Status updated successfully.
2025-03-25 00:48:10,606 - api_logger - INFO - Resume response generated for session_id: new-test7, response_time: 9ms
2025-03-25 00:48:18,632 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test7 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:48:20,203 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test7 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:48:20,211 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test7
2025-03-25 00:48:20,470 - api_logger - INFO - Job status for session new-test7: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-25 00:48:20,470 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test7
2025-03-25 00:48:20,471 - api_logger - INFO - Processing interview for session_id: new-test7
2025-03-25 00:48:20,471 - api_logger - INFO - Fetching requests for session_id: new-test7
2025-03-25 00:48:20,732 - api_logger - INFO - Extracting questions from responses for session_id: new-test7
2025-03-25 00:48:20,735 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-25 00:48:20,735 - api_logger - INFO - Fetching role and experience level for session_id: new-test7
2025-03-25 00:48:20,992 - api_logger - INFO - SQL result: [{'role': 'data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-25 00:48:20,992 - api_logger - INFO - Role: data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test7
2025-03-25 00:48:20,992 - api_logger - INFO - Creating interview experience messages.
2025-03-25 00:48:20,992 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-25 00:48:22,712 - api_logger - INFO - Interview experience messages created successfully.
2025-03-25 00:48:22,712 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:48:25,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:48:25,481 - api_logger - INFO - Response received successfully.
2025-03-25 00:48:25,481 - api_logger - INFO - Inserting record for session_id: new-test7, request_type: interview_experience
2025-03-25 00:48:25,973 - api_logger - INFO - Extracted questions for session_id: new-test7: [{'request_type': 'job_title', 'questions': ['What programming languages are you proficient in for data analysis and why do you prefer them?', 'Can you discuss your experience with data wrangling and cleaning techniques?', 'Describe a complex data analysis project you have worked on. What methodologies did you use?', 'What machine learning algorithms are you most comfortable with and can you provide examples of when you have applied them?', 'How do you approach feature engineering and why is it important in the data science process?', 'Can you explain your experience with statistical analysis and how you apply it in your data science projects?', 'Describe your familiarity with big data technologies like Hadoop or Spark. How have you used them in your work?', 'How do you validate and test your data models to ensure their accuracy and reliability?', 'What tools and libraries do you prefer for data visualization and why?', 'Describe a situation where you had to present complex data findings to a non-technical audience. How did you ensure they understood?', 'What is your experience with version control systems in the context of data science?', 'How do you stay updated on the latest trends and technologies in data science?', 'Can you talk about a specific instance where you identified a key insight from data that had a substantial impact on a project or decision?', 'What steps do you take when faced with incomplete or messy data?', 'How do you approach cross-validation when developing predictive models?', 'What metrics do you use to evaluate model performance and why?', 'Could you describe your typical workflow for a data science project from start to finish?', 'Have you had experience in deploying machine learning models into production? Can you describe that process?', 'What is your experience with cloud computing services (e.g., AWS, Google Cloud) in the context of data science?', 'Describe your experience with collaborating with cross-functional teams in your previous data science roles. How do you ensure alignment?', 'How do you prioritize competing tasks in a data science project, and how do you manage your time and resources effectively?', 'How do you approach training or mentoring team members who may not be as proficient in data science?']}, {'request_type': 'jd', 'questions': ['Why do you want to work here?', 'Do you have any experience with turning raw data into actionable insights?', 'What is your experience with data analysis and statistical methods?', 'This role involves structuring unformatted data. How would you go about structuring data to make it presentable?', 'At our company, we use Python for data analysis. How much experience do you have with Python, and in what context have you used it before?', 'What experience do you have using data visualization tools such as Tableau?', 'In this position, you will be building algorithms and machine learning models. Do you have any experience with this? Can you tell me about it?', 'How would you explain what machine learning is to someone with no prior experience?', 'Can you share an instance where you encountered a significant challenge while working with data and describe the steps you took to address it?', 'Have you ever taken the lead on a project or initiative? How did you motivate and guide your team to achieve success?']}, {'request_type': 'resume', 'questions': ['What was your favorite course at Indian Institute of Information Technology (IIIT) Vadodara?', 'Your resume mentioned that you have experience in Natural Language Processing (NLP). How have you utilized this skill in your previous roles?', 'Can you tell me more about your time at FutureSmart AI? Describe your role and responsibilities there.', 'Tell me more about how you enhanced data analytics capabilities for high-profile clients at FutureSmart AI.', 'Your resume mentioned that you achieved a 30% boost in process efficiency while developing NLP applications. Could you provide more details on how you accomplished this?', 'How has your previous experience at FutureSmart AI prepared you for this role?', 'How will your expertise in Python contribute to your success in this role?', 'How did you handle developing the RL Natural Language to SQL Query Retrieval project while at FutureSmart AI?', 'On your resume, you mentioned that you engineered a Resume Shortlisting system. Could you describe your typical approach to utilizing this skill?', 'Your resume mentioned that you led the integration of sentiment analysis for intent detection. Could you provide more details on how you accomplished this?', 'Tell me about a time when you faced a setback or challenge at work. How did you handle it, and what did you learn from that experience?', 'Describe a scenario where you had to adapt to changes in a project scope or timeline. How did you manage the adjustments, and what was the outcome?']}]
2025-03-25 00:48:25,977 - api_logger - INFO - Interview Experience Request with Session ID: new-test7 Successfully processed
2025-03-25 00:48:42,404 - api_logger - INFO - Fetching session data for session ID: new-test7 and is_ca: False
2025-03-25 00:48:44,132 - api_logger - INFO - Fetching session data for session_id: new-test7
2025-03-25 00:48:44,684 - api_logger - INFO - Session data fetched successfully for session ID: new-test7
2025-03-25 00:49:10,035 - api_logger - INFO - Fetching prompt for request type: RequestType.job_title
2025-03-25 00:49:10,035 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-25 00:49:11,793 - api_logger - INFO - Prompt retrieved successfully.
2025-03-25 00:54:26,148 - api_logger - INFO - Request received for Job Title with Session ID: new-test8 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-25 00:54:27,946 - api_logger - INFO - Job Title Request with Session ID: new-test8 Successfully received and added to background task
2025-03-25 00:54:27,951 - api_logger - INFO - Starting background response generation for session_id: new-test8, job_title: data Scientist
2025-03-25 00:54:27,952 - api_logger - INFO - Checking if session does not exist for session_id: new-test8, request_type: job_title
2025-03-25 00:54:28,221 - api_logger - INFO - No previous session id
2025-03-25 00:54:28,221 - api_logger - INFO - Inserting status for session_id: new-test8, request_type: job_title, status: Inprogress
2025-03-25 00:54:28,842 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-25 00:54:30,453 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:54:37,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:54:37,321 - api_logger - INFO - Response received successfully.
2025-03-25 00:54:37,321 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test8
2025-03-25 00:54:37,322 - api_logger - INFO - Inserting record for session_id: new-test8, request_type: job_title
2025-03-25 00:54:37,873 - api_logger - INFO - Inserted initial status for session_id: new-test8
2025-03-25 00:54:37,873 - api_logger - INFO - Updating status for session_id: new-test8, request_type: job_title, status: complete
2025-03-25 00:54:38,443 - api_logger - INFO - Status updated successfully.
2025-03-25 00:54:38,443 - api_logger - INFO - Response generated for session_id: new-test8, response_time: 9ms
2025-03-25 00:54:42,266 - api_logger - INFO - Request received for JD with Session ID: new-test8 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:54:44,169 - api_logger - INFO - Getting file text for: temp\uploaded_ds_jd.pdf
2025-03-25 00:54:44,182 - api_logger - INFO - Processing PDF file.
2025-03-25 00:54:44,572 - api_logger - INFO - File text extraction completed.
2025-03-25 00:54:44,572 - api_logger - INFO - JD Request with Session ID: new-test8 Successfully received and added to background task
2025-03-25 00:54:44,573 - api_logger - INFO - Processing JD upload for session_id: new-test8
2025-03-25 00:54:44,573 - api_logger - INFO - Checking if session does not exist for session_id: new-test8, request_type: jd
2025-03-25 00:54:44,853 - api_logger - INFO - No previous session id
2025-03-25 00:54:44,853 - api_logger - INFO - Inserting status for session_id: new-test8, request_type: jd, status: Inprogress
2025-03-25 00:54:45,423 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-25 00:54:47,402 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:54:52,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:54:52,143 - api_logger - INFO - Response received successfully.
2025-03-25 00:54:52,143 - api_logger - INFO - Inserting record for session_id: new-test8, request_type: jd
2025-03-25 00:54:52,692 - api_logger - INFO - JD response generated for session_id: new-test8, response_time: 7.569783926010132s
2025-03-25 00:54:52,692 - api_logger - INFO - Updating status for session_id: new-test8, request_type: jd, status: complete
2025-03-25 00:54:53,262 - api_logger - INFO - Status updated successfully.
2025-03-25 00:54:54,862 - api_logger - INFO - Request received for Resume with Session ID: new-test8 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:54:56,623 - api_logger - INFO - Resume Request with Session ID: new-test8 Successfully received and added to background task
2025-03-25 00:54:56,632 - api_logger - INFO - Processing resume for session_id: new-test8
2025-03-25 00:54:56,632 - api_logger - INFO - Checking if session does not exist for session_id: new-test8, request_type: resume
2025-03-25 00:54:57,004 - api_logger - INFO - No previous session id found for session_id: new-test8
2025-03-25 00:54:57,004 - api_logger - INFO - Inserting status for session_id: new-test8, request_type: resume, status: Inprogress
2025-03-25 00:54:57,576 - api_logger - INFO - Getting file text for: temp\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-25 00:54:57,601 - api_logger - INFO - Processing PDF file.
2025-03-25 00:54:57,741 - api_logger - INFO - File text extraction completed.
2025-03-25 00:54:57,741 - api_logger - INFO - Successfully extracted resume text for session_id: new-test8
2025-03-25 00:54:57,741 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-25 00:54:59,682 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:55:04,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:55:04,700 - api_logger - INFO - Response received successfully.
2025-03-25 00:55:04,701 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test8
2025-03-25 00:55:04,702 - api_logger - INFO - Inserting record for session_id: new-test8, request_type: resume
2025-03-25 00:55:05,277 - api_logger - INFO - Updating status for session_id: new-test8, request_type: resume, status: complete
2025-03-25 00:55:05,961 - api_logger - INFO - Status updated successfully.
2025-03-25 00:55:05,962 - api_logger - INFO - Resume response generated for session_id: new-test8, response_time: 8ms
2025-03-25 00:55:17,157 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test8 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:55:18,963 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test8 Model: gpt-4o-mini and is_ca: False
2025-03-25 00:55:18,963 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test8
2025-03-25 00:55:19,243 - api_logger - INFO - Job status for session new-test8: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-25 00:55:19,243 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test8
2025-03-25 00:55:19,243 - api_logger - INFO - Processing interview for session_id: new-test8
2025-03-25 00:55:19,243 - api_logger - INFO - Fetching requests for session_id: new-test8
2025-03-25 00:55:19,561 - api_logger - INFO - Extracting questions from responses for session_id: new-test8
2025-03-25 00:55:19,562 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-25 00:55:19,562 - api_logger - INFO - Fetching role and experience level for session_id: new-test8
2025-03-25 00:55:19,868 - api_logger - INFO - SQL result: [{'role': 'data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-25 00:55:19,868 - api_logger - INFO - Role: data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test8
2025-03-25 00:55:19,868 - api_logger - INFO - Creating interview experience messages.
2025-03-25 00:55:19,868 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-25 00:55:21,742 - api_logger - INFO - Interview experience messages created successfully.
2025-03-25 00:55:21,742 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-25 00:55:24,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-25 00:55:24,372 - api_logger - INFO - Response received successfully.
2025-03-25 00:55:24,372 - api_logger - INFO - Inserting record for session_id: new-test8, request_type: interview_experience
2025-03-25 00:55:24,953 - api_logger - INFO - Extracted questions for session_id: new-test8: [{'request_type': 'job_title', 'questions': ['What programming languages are you proficient in, and how have you utilized them in your data science projects?', 'Can you describe your experience with data manipulation libraries such as Pandas or NumPy?', 'What statistical methods do you find most useful in data analysis and why?', 'How comfortable are you with machine learning algorithms? Could you explain a specific algorithm you have implemented?', 'Can you discuss a project where you used data visualization tools? What insights did you gain from that experience?', 'What approaches do you take to ensure the quality and cleanliness of data before analysis?', 'Could you describe a time when you had to work with a large dataset? What challenges did you face, and how did you overcome them?', 'What experience do you have with data modeling and how have you applied it in practical scenarios?', 'How do you stay updated with the latest trends and technologies in data science?', 'Describe a situation where you had to work with stakeholders to understand their data needs. How did you approach it?', 'What techniques do you use to communicate your findings to non-technical stakeholders?', 'How would you evaluate the performance of a machine learning model? What metrics do you consider?', 'Can you discuss your experience with deep learning frameworks like TensorFlow or PyTorch?', 'What is your experience with version control systems, and why do you think they are important for data scientists?', 'How do you approach training or mentoring team members who may not be as proficient in data science skills?', 'Describe a situation where you had to prioritize competing tasks related to data science, and how did you manage your time and resources effectively.']}, {'request_type': 'jd', 'questions': ['Why do you want to work here?', 'Do you have any experience with data mining?', 'What is your experience with machine learning?', 'This role involves preparing data presentations after necessary data processing. How would you ensure the data is presented effectively?', 'At our company, we use Python for data analysis. How much experience do you have with Python, and in what context have you used it before?', 'In this position, you will be developing and maintaining databases. Do you have any experience in this area? Can you tell me about it?', 'How would you explain what machine learning is to someone with no prior experience?', 'Can you share an instance where you encountered a significant challenge while analyzing data and describe the steps you took to address it?', 'Have you ever taken the lead on a project or initiative? How did you motivate and guide your team to achieve success?']}, {'request_type': 'resume', 'questions': ['What was your favorite course at Indian Institute of Information Technology (IIIT) Vadodara?', 'Your resume mentioned that you have experience in generative AI. How have you utilized this skill in your previous roles?', 'Can you tell me more about your time at FutureSmart AI? Describe your role and responsibilities there.', 'Tell me more about how you enhanced data analytics capabilities for high-profile clients at FutureSmart AI.', 'What are some of the lessons you learned while at Indian Institute of Information Technology that would be relevant to the roles you are applying for?', 'How has your previous experience at FutureSmart AI prepared you for this role?', 'How will your expertise in Python and machine learning contribute to your success in this role?', 'How did you handle the development and integration of NLP applications while at FutureSmart AI?', 'On your resume, you mentioned that you achieved a 75% accuracy in SQL from natural language. Could you provide more details on how you accomplished this?', 'I noticed that you have a certification. What is one valuable lesson you learned while earning that certification?', 'How did you achieve a 30% boost in process efficiency and response accuracy for your clients at FutureSmart AI?', 'Tell me about a time when you faced a setback or challenge at work. How did you handle it, and what did you learn from that experience?', 'Describe a scenario where you had to adapt to changes in a project scope or timeline. How did you manage the adjustments, and what was the outcome?']}]
2025-03-25 00:55:24,961 - api_logger - INFO - Interview Experience Request with Session ID: new-test8 Successfully processed
2025-03-25 00:55:42,039 - api_logger - INFO - Fetching session data for session ID: new-test8 and is_ca: False
2025-03-25 00:55:43,793 - api_logger - INFO - Fetching session data for session_id: new-test8
2025-03-25 00:55:44,312 - api_logger - INFO - Session data fetched successfully for session ID: new-test8
2025-03-25 00:55:48,456 - api_logger - INFO - Fetching prompt for request type: RequestType.job_title
2025-03-25 00:55:48,456 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-25 00:55:50,462 - api_logger - INFO - Prompt retrieved successfully.
