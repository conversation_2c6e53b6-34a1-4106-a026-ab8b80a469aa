2025-04-17 18:19:50,282 - api_logger - INFO - Fetching prompt for request type: RequestType.feedback
2025-04-17 18:21:53,741 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:21:53,741 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:22:01,209 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:22:01,335 - api_logger - INFO - Response received successfully.
2025-04-17 18:44:31,531 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:44:31,532 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:44:35,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:44:35,951 - api_logger - INFO - Response received successfully.
2025-04-17 18:44:35,955 - api_logger - ERROR - Error generating feedback: Response missing required fields
2025-04-17 18:47:33,543 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:47:33,543 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:47:37,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:47:37,871 - api_logger - INFO - Response received successfully.
2025-04-17 18:50:37,239 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:50:37,240 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:50:42,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:50:42,395 - api_logger - INFO - Response received successfully.
2025-04-17 18:52:32,967 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:52:32,973 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:52:37,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:52:37,657 - api_logger - INFO - Response received successfully.
2025-04-17 18:52:53,664 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:52:53,676 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:53:02,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:53:02,741 - api_logger - INFO - Response received successfully.
2025-04-17 18:56:35,808 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 18:56:35,816 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 18:56:47,628 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 18:56:47,690 - api_logger - INFO - Response received successfully.
2025-04-17 19:15:01,467 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 19:15:01,467 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 19:15:05,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 19:15:06,056 - api_logger - INFO - Response received successfully.
2025-04-17 19:16:41,663 - api_logger - INFO - Generating feedback for question: What is your strength
2025-04-17 19:16:41,663 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 19:16:45,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 19:16:45,552 - api_logger - INFO - Response received successfully.
2025-04-17 19:21:43,593 - api_logger - INFO - Generating feedback for question: What is your strength in coding
2025-04-17 19:21:43,594 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 19:21:47,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 19:21:47,459 - api_logger - INFO - Response received successfully.
2025-04-17 21:23:19,163 - api_logger - INFO - Request received for Job Title with Session ID: new-1 Model: gpt-4o-mini and is_ca: False and Experience Level: mid and Number of Questions: 7
2025-04-17 21:23:24,764 - api_logger - INFO - Job Title Request with Session ID: new-1 Successfully received and added to background task
2025-04-17 21:23:24,780 - api_logger - INFO - Starting background response generation for session_id: new-1, job_title: data scientist
2025-04-17 21:23:24,780 - api_logger - INFO - Checking if session does not exist for session_id: new-1, request_type: job_title
2025-04-17 21:23:25,040 - api_logger - INFO - No previous session id
2025-04-17 21:23:25,040 - api_logger - INFO - Inserting status for session_id: new-1, request_type: job_title, status: Inprogress
2025-04-17 21:23:25,559 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-17 21:23:27,457 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 21:23:36,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 21:23:36,440 - api_logger - INFO - Response received successfully.
2025-04-17 21:23:36,440 - api_logger - INFO - Succesfully generated job title questions for session_id: new-1
2025-04-17 21:23:36,440 - api_logger - INFO - Inserting record for session_id: new-1, request_type: job_title
2025-04-17 21:23:37,062 - api_logger - INFO - Inserted initial status for session_id: new-1
2025-04-17 21:23:37,070 - api_logger - INFO - Updating status for session_id: new-1, request_type: job_title, status: complete
2025-04-17 21:23:37,591 - api_logger - INFO - Status updated successfully.
2025-04-17 21:23:37,591 - api_logger - INFO - Response generated for session_id: new-1, response_time: 11ms
2025-04-17 21:24:03,583 - api_logger - INFO - Fetching session data for session ID: new-1 and is_ca: False
2025-04-17 21:24:05,181 - api_logger - INFO - Fetching session data for session_id: new-1
2025-04-17 21:24:05,464 - api_logger - INFO - Session data fetched successfully for session ID: new-1
2025-04-17 21:26:34,920 - api_logger - INFO - Generating feedback for question: What is your strength in coding
2025-04-17 21:26:34,921 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-17 21:26:38,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-17 21:26:38,884 - api_logger - INFO - Response received successfully.
