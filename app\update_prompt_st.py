import streamlit as st
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from api.database.prompt_db import fetch_latest_prompt, update_prompt_in_db

def display_required_variables(prompt_type):
    if prompt_type == "job_title":
        st.info("Required variables: {experience_note}, {role}")
    elif prompt_type == "resume":
        st.info("Required variables: {role_string}")
    elif prompt_type == "interview_experience":
        st.info("Required variables: {n}, {experience_level}, {role}, {categories_list}")
    elif prompt_type == "jd":
        st.info("Required variables: No required variables")
    else:
        st.warning("Select a prompt type to see required variables.")

# Streamlit app
def main():
    st.title("Prompt Updater")
    # Select the prompt type
    selected_request_type = st.selectbox("Select Prompt Type", ["job_title", "jd", "resume", "interview_experience",])
    
    display_required_variables(selected_request_type)
    # Fetch existing prompt from the database
    existing_prompt = get_prompt_from_database(selected_request_type)
    # Display the existing prompt in an editable text area
    updated_prompt = st.text_area("Edit Prompt", existing_prompt, height=250, max_chars=5000)

    # Button to submit the updated prompt
    if st.button("Submit"):
        # Update the prompt in the mockup dictionary (replace with database update logic)
        # prompts[selected_request_type] = updated_prompt
        if existing_prompt != updated_prompt:
            # Check for the presence of required variables before inserting the prompt data
            if selected_request_type == "job_title":
                if "{experience_note}" not in updated_prompt or "{role}" not in updated_prompt:
                    st.error("Missing required variables in the job_title prompt")
                    return
            elif selected_request_type == "resume":
                if "{role_string}" not in updated_prompt:
                    st.error("Missing required variables in the resume prompt")
                    return
            elif selected_request_type == "interview_experience":
                # Check for the presence of required variables
                required_variables = ["{n}", "{experience_level}", "{role}", "{categories_list}"]
                if not all(variable in updated_prompt for variable in required_variables):
                    st.error("Missing required variables in the interview_experience prompt")
                    return

            update_prompt_in_db(selected_request_type,updated_prompt)
            st.success("Prompt updated successfully")
        else:
            st.info("No changes made to the prompt")

# Function to get existing prompt from the database
def get_prompt_from_database(prompt_type):
    try:
        # Fetch prompt from the database using the get_prompt function
        return fetch_latest_prompt(prompt_type)
    except Exception as e:
        st.error(f"Error retrieving prompt from database: {str(e)}")
        return None

if __name__ == "__main__":
    main()
