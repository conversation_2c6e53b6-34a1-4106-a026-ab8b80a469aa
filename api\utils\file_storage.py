"""
This module provides functions for file-based storage of feedback examples.
"""

import json
import os
import csv
import io
from typing import List, Tu<PERSON>, Dict, Any
from api.common.pydantic_models import FeedbackExample, FeedbackExampleCreate
from api.common.api_logger import api_logger as logger

# Define the storage directory and file
STORAGE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
FEEDBACK_FILE = os.path.join(STORAGE_DIR, "feedback_examples.json")

def ensure_storage_dir():
    """Ensure the storage directory exists"""
    if not os.path.exists(STORAGE_DIR):
        os.makedirs(STORAGE_DIR)
        logger.info(f"Created storage directory: {STORAGE_DIR}")

def load_feedback_examples() -> List[FeedbackExample]:
    """
    Load feedback examples from the JSON file.

    Returns:
        List[FeedbackExample]: List of feedback examples
    """
    ensure_storage_dir()
    try:
        if os.path.exists(FEEDBACK_FILE):
            with open(FEEDBACK_FILE, 'r') as f:
                data = json.load(f)
                return [FeedbackExample(**item) for item in data]
        else:
            logger.info(f"Feedback file not found at {FEEDBACK_FILE}, returning empty list")
            return []
    except Exception as e:
        logger.error(f"Error loading feedback examples: {e}")
        return []

def save_feedback_examples(examples: List[FeedbackExample]) -> bool:
    """
    Save feedback examples to the JSON file.

    Args:
        examples (List[FeedbackExample]): List of feedback examples to save

    Returns:
        bool: True if saved successfully, False otherwise
    """
    ensure_storage_dir()
    try:
        # Convert FeedbackExample objects to dictionaries
        data = [example.model_dump() for example in examples]
        with open(FEEDBACK_FILE, 'w') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Saved {len(examples)} feedback examples to {FEEDBACK_FILE}")
        return True
    except Exception as e:
        logger.error(f"Error saving feedback examples: {e}")
        return False

def add_feedback_example(example: FeedbackExample) -> Tuple[bool, FeedbackExample]:
    """
    Add a new feedback example to the file.

    Args:
        example (FeedbackExample): The example to add

    Returns:
        Tuple[bool, FeedbackExample]: (success status, added example with ID)
    """
    examples = load_feedback_examples()

    # Generate an auto-increment ID (max ID + 1)
    next_id = 1  # Default starting ID
    if examples:
        # Convert existing IDs to integers where possible, ignore non-numeric IDs
        existing_ids = []
        for ex in examples:
            try:
                existing_ids.append(int(ex.id))
            except (ValueError, TypeError):
                pass

        if existing_ids:
            next_id = max(existing_ids) + 1

    # Create a copy with the new ID
    example_with_id = example.model_copy()
    example_with_id.id = str(next_id)

    examples.append(example_with_id)
    return save_feedback_examples(examples), example_with_id

def delete_feedback_example(example_id: str) -> bool:
    """
    Delete a feedback example from the file.

    Args:
        example_id (str): The unique ID of the example to delete

    Returns:
        bool: True if deletion was successful, False if not found or error
    """
    examples = load_feedback_examples()
    original_length = len(examples)
    examples = [ex for ex in examples if ex.id != example_id]

    if len(examples) == original_length:
        logger.info(f"No feedback example found with ID: {example_id}")
        return False

    return save_feedback_examples(examples)

def export_feedback_examples_to_csv() -> Tuple[bool, io.StringIO]:
    """
    Export all feedback examples to CSV format.

    Returns:
        Tuple[bool, io.StringIO]: (success status, CSV data as StringIO)
    """
    try:
        examples = load_feedback_examples()
        if not examples:
            logger.info("No feedback examples to export")
            # Return an empty CSV with headers
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(['question', 'answer', 'suggestions', 'proposed_answer'])
            output.seek(0)
            return True, output

        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['question', 'answer', 'suggestions', 'proposed_answer'])

        # Write data rows
        for example in examples:
            writer.writerow([
                example.question,
                example.answer,
                example.suggestions,
                example.proposed_answer
            ])

        output.seek(0)  # Reset file pointer to beginning
        logger.info(f"Successfully exported {len(examples)} feedback examples to CSV")
        return True, output
    except Exception as e:
        logger.error(f"Error exporting feedback examples to CSV: {e}")
        return False, io.StringIO()

def import_feedback_examples_from_csv(csv_file: io.BytesIO) -> Tuple[bool, int]:
    """
    Import feedback examples from a CSV file.
    This will overwrite all existing feedback examples with the ones from the CSV.

    Args:
        csv_file (io.BytesIO): CSV file content as BytesIO

    Returns:
        Tuple[bool, int]: (success status, number of examples imported)
    """
    try:
        # Decode bytes to string
        content = csv_file.read().decode('utf-8')
        csv_file = io.StringIO(content)

        # Parse CSV
        reader = csv.DictReader(csv_file)

        # Check if required columns exist
        required_columns = ['question', 'answer', 'suggestions', 'proposed_answer']
        if not all(col in reader.fieldnames for col in required_columns):
            missing = [col for col in required_columns if col not in reader.fieldnames]
            logger.error(f"CSV is missing required columns: {missing}")
            return False, 0

        # Create a new list to store the imported examples
        new_examples = []

        # Process rows
        count = 0
        next_id = 1

        for row in reader:
            # Create a new feedback example
            example = FeedbackExample(
                id=str(next_id),  # Assign sequential IDs
                question=row['question'],
                answer=row['answer'],
                suggestions=row['suggestions'],
                proposed_answer=row['proposed_answer']
            )

            new_examples.append(example)
            next_id += 1
            count += 1

        # Save the new examples (overwriting existing ones)
        success = save_feedback_examples(new_examples)

        if success:
            logger.info(f"Successfully imported {count} feedback examples from CSV (replaced all existing examples)")
        else:
            logger.error("Failed to save imported feedback examples")
            return False, 0

        return True, count
    except Exception as e:
        logger.error(f"Error importing feedback examples from CSV: {e}")
        return False, 0