# TTS Audio Generation API Documentation

## Overview

The Audio Generation API provides text-to-speech (TTS) functionality using ElevenLabs API. It allows you to convert interview questions into high-quality audio files with different voice personas.

## Features

- **Multiple Voice Personas**: 5 different interviewer voices mapped to visual personas
- **Background Processing**: Asynchronous audio generation for multiple questions
- **Job Tracking**: Real-time progress monitoring with unique job IDs
- **File Management**: Automatic file organization and cleanup
- **Direct Downloads**: Individual audio file downloads via FastAPI

## Voice Mapping

| Voice ID | Persona | Description |
|----------|---------|-------------|
| `RACHEL` | Asian Women | Professional, clear voice |
| `CLARITY` | Black Women | Confident, articulate voice |
| `BRIAN` | White Male | Authoritative, professional voice |
| `DONTAVIOUS` | Black Male (Casual) | Friendly, approachable voice |
| `GEORGE` | Black Male (Formal) | Formal, business-like voice |

## API Endpoints

### 1. Generate Audio
**POST** `/audio/generate-audio`

Generate audio files for a list of questions.

**Request Body:**
```json
{
  "questions": [
    "Tell me about yourself",
    "What are your strengths?",
    "Where do you see yourself in 5 years?"
  ],
  "voice_id": "21m00Tcm4TlvDq8ikWAM",
  "model": "eleven_flash_v2_5"
}
```

**Response:**
```json
{
  "job_id": "audio_job_a1b2c3d4e5f6",
  "status": "pending",
  "total_questions": 3,
  "audio_files": [],
  "created_at": "2024-01-15T10:30:00",
  "voice_used": "21m00Tcm4TlvDq8ikWAM"
}
```

### 2. Check Job Status
**GET** `/audio/job-status/{job_id}`

Get current status and progress of an audio generation job.

**Response:**
```json
{
  "job_id": "audio_job_a1b2c3d4e5f6",
  "status": "completed",
  "progress": 100,
  "audio_files": [
    {
      "question_index": 0,
      "question_text": "Tell me about yourself",
      "file_path": "/audio_files/audio_job_a1b2c3d4e5f6/question_1_abc123.mp3",
      "file_url": "/audio/download/audio_job_a1b2c3d4e5f6/question_1_abc123.mp3",
      "duration_seconds": 12.5
    }
  ],
  "error_message": null
}
```

### 3. Download Audio File
**GET** `/audio/download/{job_id}/{filename}`

Download a specific audio file.

**Response:** Audio file (MP3 format)

### 4. Get Available Voices
**GET** `/audio/voices`

Get list of available voices and supported models.

**Response:**
```json
{
  "voices": {
    "21m00Tcm4TlvDq8ikWAM": "Rachel - Asian Women",
    "zbj5pYu7PWmTR3zNpMct": "Clarity Speaks - Black Women",
    "nPczCjzI2devNBz1zQrb": "Brian - White Male",
    "gUot1J0p7f1TAO8rUA9w": "Dontavious Breighton - Black Male (Casual)",
    "JBFqnCBsd6RMkjVDRZzb": "George - Black Male (Formal)"
  },
  "default_model": "eleven_flash_v2_5",
  "supported_models": [
    "eleven_flash_v2_5",
    "eleven_multilingual_v2",
    "eleven_turbo_v2_5"
  ]
}
```

### 5. Delete Job
**DELETE** `/audio/job/{job_id}`

Delete an audio generation job and clean up associated files.

**Response:**
```json
{
  "message": "Job audio_job_a1b2c3d4e5f6 deleted successfully"
}
```

## Job Status Values

| Status | Description |
|--------|-------------|
| `pending` | Job created, waiting to start processing |
| `processing` | Currently generating audio files |
| `completed` | All audio files generated successfully |
| `partial` | Some audio files generated, some failed |
| `failed` | Job failed completely |

## Usage Examples

### Basic Usage Flow

1. **Generate Audio**
```bash
curl -X POST "http://localhost:8001/audio/generate-audio" \
  -H "Content-Type: application/json" \
  -H "API_KEY: your-api-key" \
  -H "API_SECRET: your-api-secret" \
  -d '{
    "questions": ["Tell me about yourself", "What are your strengths?"],
    "voice_id": "21m00Tcm4TlvDq8ikWAM",
    "model": "eleven_flash_v2_5"
  }'
```

2. **Check Progress**
```bash
curl "http://localhost:8001/audio/job-status/audio_job_a1b2c3d4e5f6"
```

3. **Download Files**
```bash
curl "http://localhost:8001/audio/download/audio_job_a1b2c3d4e5f6/question_1_abc123.mp3" \
  -o question_1.mp3
```

### Integration with Interview Experience

```python
# After getting interview questions
questions = get_interview_experience(session_id)["questions"]

# Generate audio for first 3 questions
audio_request = {
    "questions": questions[:3],
    "voice_id": "21m00Tcm4TlvDq8ikWAM",  # Rachel - Asian Women
    "model": "eleven_flash_v2_5"
}

response = requests.post("/audio/generate-audio", json=audio_request)
job_id = response.json()["job_id"]

# Poll for completion
while True:
    status = requests.get(f"/audio/job-status/{job_id}").json()
    if status["status"] in ["completed", "failed"]:
        break
    time.sleep(2)

# Get audio URLs
audio_files = status["audio_files"]
```

## Configuration

### Environment Variables
Add to your `.env` file:
```
elevenlabs_api_key=your_elevenlabs_api_key_here
```

### Dependencies
Add to `requirements.txt`:
```
elevenlabs==1.14.0
aiofiles
```

## File Storage Structure

```
audio_files/
├── audio_job_a1b2c3d4e5f6/
│   ├── question_1_abc123.mp3
│   ├── question_2_def456.mp3
│   └── question_3_ghi789.mp3
└── audio_job_x7y8z9w0v1u2/
    ├── question_1_jkl012.mp3
    └── question_2_mno345.mp3
```

## Best Practices

### For Client Applications

1. **Polling Strategy**: Check job status every 2-3 seconds
2. **Error Handling**: Always check for `failed` status and handle errors
3. **File Management**: Download and cache audio files locally
4. **Cleanup**: Delete jobs after downloading files to save server space

### For Production Deployment

1. **Storage**: Consider migrating to S3 for scalable file storage
2. **Cleanup**: Implement automated cleanup of old jobs (24-48 hours)
3. **Rate Limiting**: Add rate limiting for audio generation requests
4. **Monitoring**: Monitor ElevenLabs API usage and costs

## Limitations

- Maximum 50 questions per request
- Audio files stored locally (consider S3 for production)
- In-memory job tracking (consider database for production)
- No authentication on download endpoints (add if needed)

## Cost Considerations

- ElevenLabs charges per character processed
- `eleven_flash_v2_5` model is cost-effective for most use cases
- Monitor usage through ElevenLabs dashboard
- Consider caching frequently used questions

## Error Handling

The API returns standard HTTP status codes:
- `200`: Success
- `400`: Bad request (invalid input)
- `404`: Job or file not found
- `500`: Internal server error

Error responses include descriptive messages:
```json
{
  "detail": "Questions list cannot be empty"
}
```
