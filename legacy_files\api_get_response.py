import os
import tempfile
import time
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi import BackgroundTasks
import sys

from fastapi import FastAPI, UploadFile
import numpy as np
import openai
from qa_model import generate_response

from utils import get_audio_initial_message, get_file_text, get_file_text_fastapi, get_jd_initial_message, get_resume_and_JD_initial_message, get_resume_initial_message, get_resume_jd_role_initial_message, get_role_initial_message

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow any origin
    allow_credentials=False,  # Must be False if allow_origins is set to wildcard
    allow_methods=["*"],  # Allow all methods (GET, POST, PUT, DELETE, etc.)
    allow_headers=["*"],  # Allow all headers
)

# jd can be optional
@app.post("/upload_resume_and_jd/")
async def upload_resume_and_jd(resume: UploadFile, jd: UploadFile = None, jd_text: str = None, job_title: str = None):
    try:
        model = "gpt-4"
        temp_dir = "data"

        resume_file_name = f"uploaded_{resume.filename}"
        resume_file_name = os.path.join(temp_dir, resume_file_name)

        with open(resume_file_name, "wb") as f1:
            f1.write(resume.file.read())

        # resume_file = open(resume_file_name, "rb")
        # jd_file = open(jd_file_name, "rb")
        # resume_text = get_file_text(resume_file)
        resume_text = get_file_text_fastapi(resume_file_name)

        text = "Parsed text from Resume: \n\n"
        text = text + resume_text
        text = text + "\nParsed text from Job Description: \n\n"
        if jd_text:
            text = text + jd_text
        elif jd:
            jd_file_name = f"uploaded_{jd.filename}"
            jd_file_name = os.path.join(temp_dir, jd_file_name)
            with open(jd_file_name, "wb") as f2:
                f2.write(jd.file.read())
            text = text + get_file_text_fastapi(jd_file_name)
        else:
            return {"status": False, "error": "No JD text or file provided"}
        if job_title:
            messages = get_resume_jd_role_initial_message(text, job_title)
        else:
            messages = get_resume_and_JD_initial_message(text)
        res = generate_response(messages,model)
        result_dict = {"questions": res, "status": True}

        return result_dict
    except Exception as e:
        print("Exception: ", e)
        return {"status": False, "error": str(e)}
    

@app.post("/upload_resume/")
def upload_resume(resume: UploadFile):
    try:
        model = "gpt-4"

        resume_file_name = f"uploaded_{resume.filename}"
        resume_file_name = os.path.join("data", resume_file_name)
        with open(resume_file_name, "wb") as f1:
            f1.write(resume.file.read())

        # resume_file = open(resume_file_name, "rb")
        resume_text = get_file_text_fastapi(resume_file_name)
        messages = get_resume_initial_message(resume_text)
        res = generate_response(messages,model)
        result_dict = {"questions": res, "status": True}

        return result_dict
    except Exception as e:
        print("Exception: ", e)
        return {"status": False, "error": str(e)}


@app.post("/upload_jd/")
def upload_jd(jd: UploadFile = None, jd_text: str = None):
    try:
        model = "gpt-4"
        if jd_text:
            text = jd_text
        elif jd:
            jd_file_name = f"uploaded_{jd.filename}"
            jd_file_name = os.path.join("data", jd_file_name)
            with open(jd_file_name, "wb") as f2:
                f2.write(jd.file.read())
            # jd_file = open(jd_file_name, "rb")
            # text = get_file_text(jd_file)
            text = get_file_text_fastapi(jd_file_name)
        else:
            return {"status": False, "error": "No JD text or file provided"}
        messages = get_jd_initial_message(text)
        res = generate_response(messages,model)
        result_dict = {"questions": res, "status": True}

        return result_dict
    except Exception as e:
        print("Exception: ", e)
        return {"status": False, "error": str(e)}

@app.post("/upload_job_title/")
def upload_job_title(job_title: str):
    try:
        model = "gpt-4"
        messages = get_role_initial_message(job_title)
        res = generate_response(messages,model)
        result_dict = {"questions": res, "status": True}

        return result_dict
    except Exception as e:
        print("Exception: ", e)
        return {"status": False, "error": str(e)}

#@app.post("/upload_audio/")
#def upload_audio(uploaded_file: UploadFile):
    return {"status": False, "error":  "Work in progress, not ready"}
    try:
        model = "gpt-4"
        file_name = uploaded_file.filename
        print(file_name.split(".")[-1])
        # Load the sound file
        if file_name.split(".")[-1] in ["wav", "mp3"]:
            
            temp_dir = tempfile.mkdtemp()
            variable = np.random.randint(1111, 1111111)
            file_name =  f'recording{variable}.m4a'
            temp_path = os.path.join(temp_dir, file_name)
            # audio_in = AudioSegment.from_file(uploaded_file.name, format="m4a")
            with open(temp_path, "wb") as f:
                f.write(uploaded_file.read())

            audio_file = open(temp_path, "rb")
            candiate_notes = openai.Audio.translate("whisper-1", audio_file)["text"]
            # audio_file = open(uploaded_file.name, "rb")
            messages = get_audio_initial_message(audio_file)
            res = generate_response(messages,model)
            result_dict = {"questions": res, "status": True, "notes": candiate_notes}

            return result_dict
        else:
            return {"status": False, "error": "Invalid file format"}
    except Exception as e:
        print("Exception: ", e)
        return {"status": False, "error": str(e)}