2025-05-02 15:35:05,549 - api_logger - ERROR - Error loading feedback examples: 1 validation error for FeedbackExample
id
  Field required [type=missing, input_value={'question': 'What is you...wer': 'I am good in ds'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-05-02 15:35:56,018 - api_logger - INFO - Generating feedback for question: What are your skills
2025-05-02 15:35:56,018 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-05-02 15:36:05,222 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-02 15:36:12,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-02 15:36:12,118 - api_logger - INFO - Response received successfully.
2025-05-02 15:41:30,552 - api_logger - INFO - Saved 3 feedback examples to D:\FS\raj_resume\data\feedback_examples.json
2025-05-02 15:46:58,687 - api_logger - ERROR - Error loading feedback examples: Expecting value: line 1 column 1 (char 0)
2025-05-03 00:10:13,235 - api_logger - ERROR - Error loading feedback examples: Expecting value: line 1 column 1 (char 0)
2025-05-03 00:10:13,235 - api_logger - INFO - Saved 1 feedback examples to D:\FS\raj_resume\data\feedback_examples.json
