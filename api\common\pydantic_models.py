"""
This module defines Pydantic models for validating and serializing data
related to job titles, job descriptions, resumes, and interview experiences.
"""

from enum import Enum
from typing import Optional, List  # Added List import
from pydantic import BaseModel, Field, validator
from fastapi import UploadFile
from datetime import datetime

class GPTModels(Enum):
    """
    Enum for defining available GPT models.
    """
    option1 = "gpt-3.5-turbo"
    option2 = "gpt-4-turbo-preview"
    option3 = "gpt-3.5-turbo-1106"
    option4 = "gpt-4-1106-preview"
    option5 = "gpt-4o-mini"
    opttion6 = "gpt-4o"

class UploadJobTitleRequest(BaseModel):
    """
    Model for uploading job title requests, including session ID, job title,
    model, experience level, and optional Canadian request flag.
    """
    session_id: str = Field(..., description="The session ID")
    job_title: str = Field(..., description="The job title")
    model: GPTModels = Field(..., description="The GPT model to be used")
    experience_level: Optional[str] = Field(None, description="The experience level")
    #addoptional paramter is_ca which is bool
    is_ca: Optional[bool] = Field(None, description="Is the request for canada")

class UploadJDRequest(BaseModel):
    """
    Model for uploading job description requests, including session ID,
    job description text, model, and optional Canadian request flag.
    """
    session_id: str = Field(..., description="The session ID")
    jd_text: Optional[str] = Field(None, description="The job description text")
    model: GPTModels = Field(..., description="The GPT model to be used")
    is_ca: Optional[bool] = Field(None, description="Is the request for canada")

class JDText(BaseModel):
    """
    Model for representing job description text.
    """
    text: Optional[str] = Field(None, description="The job description text")

class UploadResumeRequest(BaseModel):
    """
    Model for uploading resume requests, including session ID, resume file,
    and model.
    """
    session_id: str = Field(..., description="The session ID")
    resume: UploadFile = Field(..., description="The resume")
    model: GPTModels = Field(..., description="The GPT model to be used")
    is_ca: Optional[bool] = Field(None, description="Is the request for canada")

class UploadInterviewExperienceRequest(BaseModel):
    """
    Model for uploading interview experience requests, including session ID,
    number of questions, role, model, experience level, and optional Canadian request flag.
    """
    session_id: str = Field(..., description="The session ID")
    number_of_questions: int = Field(..., description="The number of questions to be generated")
    role: str = Field(..., description="The role")
    model: GPTModels = Field(..., description="The GPT model to be used")
    experience_level: Optional[str] = Field(None, description="The experience level")
    is_ca: Optional[bool] = Field(None, description="Is the request for canada")

class SessionData(BaseModel):
    """
    Model for representing session data, including various attributes related
    to the session and its responses.
    """
    id: int
    session_id: str
    request_type: str
    role: Optional[str] = None
    gpt_response: str
    created_at: datetime  # Change these to datetime
    updated_at: datetime  # Change these to datetime
    response_time: int
    experience_level: Optional[str] = None
    model: str
    total_tokens: str
    number_of_questions: Optional[int] = None
    job_description: Optional[str] = None
    resume_text: Optional[str] = None

    @validator('created_at', 'updated_at', pre=True)
    def parse_datetime(cls, value):
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None

class QuestionList(BaseModel):
    """Model for representing a list of questions."""
    questions: List[str]

# Feedback-related models
class Feedback(BaseModel):
    """Model for representing feedback on interview answers."""
    suggestions: str = Field(description="Constructive feedback on the user's answer")
    proposed_answer: str = Field(description="A refined version of the candidate's response")

class FeedbackRequest(BaseModel):
    """Model for feedback generation request."""
    question: str = Field(description="The interview question")
    answer: str = Field(description="The candidate's answer")
    model: str = Field(default="gpt-4o-mini", description="The model to use for generation")

class FeedbackExample(BaseModel):
    """Model for storing example feedback."""
    id: str = Field(description="Unique identifier for the feedback example")
    question: str = Field(description="The interview question")
    answer: str = Field(description="The original answer")
    suggestions: str = Field(description="Improvement suggestions")
    proposed_answer: str = Field(description="Improved version of the answer")

class FeedbackExampleCreate(BaseModel):
    """Model for creating a new feedback example without an ID (which will be generated)."""
    question: str = Field(description="The interview question")
    answer: str = Field(description="The original answer")
    suggestions: str = Field(description="Improvement suggestions")
    proposed_answer: str = Field(description="Improved version of the answer")

class ApiResponse(BaseModel):
    """Model for generic API responses."""
    message: str = Field(description="Response message")
    id: Optional[str] = Field(None, description="The unique ID of the feedback example")

class CsvUploadResponse(BaseModel):
    """Model for CSV upload responses without ID field."""
    message: str = Field(description="Response message")

class DeleteFeedbackRequest(BaseModel):
    """Model for deleting feedback examples."""
    id: str = Field(description="The unique ID of the feedback example to delete")

# Audio generation models
class VoiceMapping(str, Enum):
    """Enum for ElevenLabs voice IDs mapped to interviewer personas."""
    RACHEL = "21m00Tcm4TlvDq8ikWAM"  # Asian Women
    CLARITY = "zbj5pYu7PWmTR3zNpMct"  # Black Women
    BRIAN = "nPczCjzI2devNBz1zQrb"  # White Male
    DONTAVIOUS = "gUot1J0p7f1TAO8rUA9w"  # Black Male (Casual)
    GEORGE = "JBFqnCBsd6RMkjVDRZzb"  # Black Male (Formal)

class AudioGenerationRequest(BaseModel):
    """Model for audio generation request."""
    questions: List[str] = Field(description="List of questions to convert to audio")
    voice_id: str = Field(description="ElevenLabs voice ID for the interviewer persona")
    model: str = Field(default="eleven_flash_v2_5", description="ElevenLabs model to use")

class AudioFile(BaseModel):
    """Model for individual audio file information."""
    question_index: int = Field(description="Index of the question in the original list")
    question_text: str = Field(description="The original question text")
    file_path: str = Field(description="Local file path to the audio file")
    file_url: str = Field(description="URL to download the audio file")
    duration_seconds: Optional[float] = Field(None, description="Duration of the audio in seconds")

class AudioGenerationResponse(BaseModel):
    """Model for audio generation response."""
    job_id: str = Field(description="Unique identifier for the audio generation job")
    status: str = Field(description="Status of the generation job")
    total_questions: int = Field(description="Total number of questions processed")
    audio_files: List[AudioFile] = Field(description="List of generated audio files")
    created_at: str = Field(description="Timestamp when the job was created")
    voice_used: str = Field(description="Voice ID that was used for generation")

class AudioJobStatus(BaseModel):
    """Model for checking audio job status."""
    job_id: str = Field(description="The job ID to check status for")
    status: str = Field(description="Current status of the job")
    progress: int = Field(description="Progress percentage (0-100)")
    audio_files: List[AudioFile] = Field(description="List of completed audio files")
    error_message: Optional[str] = Field(None, description="Error message if job failed")
