"""
This module manages prompts for various request types. It provides endpoints to retrieve and update prompts.
"""

from fastapi import APIRouter, Depends, Form, Query
from enum import Enum
from api.common.dependencies import get_api_key
from api.database.prompt_db import fetch_latest_prompt, update_prompt_in_db
from api.common.api_logger import api_logger as logger
router = APIRouter()

class RequestType(str, Enum):
    job_title = "job_title"
    jd = "jd"
    resume = "resume"
    interview_experience = "interview_experience"
    feedback = "feedback"  # Added feedback request type

@router.get("/get_prompt/")
def get_prompt(request_type: RequestType = Query(..., description="Select the request type to retrieve its latest prompt."), token: bool = Depends(get_api_key)):
    """
    Retrieves the latest prompt for a specified request type.
    """
    logger.info(f"Fetching prompt for request type: {request_type}")
    
    
    # For other request types, continue using database
    prompt = fetch_latest_prompt(request_type.value)
    if prompt:
        logger.info("Prompt retrieved successfully.")
        return {"status": True, "prompt": prompt}
    else:
        logger.warning(f"Prompt not found for the requested type: {request_type}")
        return {"status": False, "error": f"Prompt not found for the requested type: {request_type}"}
    
def validate_prompt_placeholders(request_type: str, prompt: str) -> bool | str:
    """Check if the prompt contains all required placeholders for its request type.
    
    Returns True if valid, otherwise returns the missing placeholder.
    """
    # Define required placeholders for each request type
    required_placeholders = {
        "resume": ["{role_string}"],
        "jd": [],  # Assuming no specific placeholders are required for job descriptions
        "job_title": ["{experience_note}", "{role}"],
        "interview_experience": ["{n}", "{experience_level}", "{role}", "{categories_list}"],
        "feedback": []  # Add any required placeholders for feedback if needed
    }
    
    # Get the list of required placeholders for the specified request type
    placeholders = required_placeholders.get(request_type, [])
    
    # Check for missing placeholders
    missing_placeholders = [placeholder for placeholder in placeholders if placeholder not in prompt]
    
    # Return True if all placeholders are present, else return the missing placeholder
    if not missing_placeholders:
        return True
    return f"Missing placeholder(s): {', '.join(missing_placeholders)}"


@router.post("/update_prompt/")
def update_prompt(request_type: RequestType = Form(...), prompt: str = Form(...), token: bool = Depends(get_api_key)):
    """
    Updates the prompt for a specified request type after validating placeholders.
    """
    logger.info(f"Updating prompt for request type: {request_type}")
    
    # Validate the prompt placeholders
    validation_result = validate_prompt_placeholders(request_type, prompt)
    if validation_result is not True:
        logger.error(f"Validation failed: {validation_result}")
        return {"status": False, "error": validation_result}
    
    # Proceed with updating the prompt if validation passes
    success = update_prompt_in_db(request_type, prompt)
    if success:
        logger.info("Prompt updated successfully.")
        return {"status": True, "message": "Prompt updated successfully"}
    else:
        logger.error("Failed to update the prompt.")
        return {"status": False, "error": "Failed to update the prompt"}
