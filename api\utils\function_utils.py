"""
This module contains utility functions for processing files and extracting
questions from responses, including functions for handling PDF and DOCX files.
"""

import PyPDF2
import io
import docx
import filetype
import json
from api.database.prompt_db import fetch_latest_prompt
from api.common.api_logger import api_logger as logger

def get_file_text_fastapi(file_path):
    """
    Extracts text from a file (PDF or DOCX) given its file path.
    Logs the process and returns the extracted content as a string.
    """
    content=""
    logger.info(f"Getting file text for: {file_path}")

    fileType = filetype.guess(file_path).mime
    # print(fileType)
    # print('File extension: %s' % fileType.extension)
    # print('File MIME type: %s' % fileType.mime)

    if fileType is None:
        logger.info('Cannot guess file type!')
        return content
    elif fileType == "application/pdf":
        logger.info('Processing PDF file.')
        pdf_reader = PyPDF2.PdfReader(file_path)
        num_pages = len(pdf_reader.pages)
        content=""
        for i in range(num_pages):
            page = pdf_reader.pages[i]
            content += page.extract_text()
    elif fileType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        logger.info('Processing DOCX file.')
        file = open(file_path, "rb")    
        doc = docx.Document(io.BytesIO(file.read())) 
        for para in doc.paragraphs:
            content+=para.text
            content+="\n"
    logger.info('File text extraction completed.')
    return content

def get_file_text(file): 
    """
    Extracts text from an uploaded file (PDF or DOCX) and logs the process.
    Returns the extracted content as a string.
    """
    fileType = file.type
    content=""
    logger.info(f"Getting file text for uploaded file of type: {fileType}")

    if fileType == "application/pdf":
        logger.info('Processing PDF file.')
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(file.read()))
        num_pages = len(pdf_reader.pages)
        for i in range(num_pages):
            page = pdf_reader.pages[i]
            content += page.extract_text()
    elif fileType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        logger.info('Processing DOCX file.')
        doc = docx.Document(io.BytesIO(file.read())) 
        for para in doc.paragraphs:
            content+=para.text
            content+="\n"

    logger.info('File text extraction completed.')
    return content

def extract_questions_from_responses(all_responses,session_id):
    """
    Extracts questions from a list of responses, where each response
    contains a JSON-formatted string under the 'gpt_response' key.
    Returns a list of extracted questions.
    """
    questions = []
    logger.info(f"Extracting questions from responses for session_id: {session_id}")

    for response in all_responses:
        # Parse the gpt_response JSON string into a Python dictionary
        gpt_response_str = response.get('gpt_response', "{}")
        request_type = response.get('request_type', "")
        try:
            #check if gpt response is empty 
            if not gpt_response_str: 
                logger.error(f"Empty GPT response for session_id: {session_id} and request_type: {request_type}")
                logger.error(f"Skipping this response for session_id: {session_id} and request_type: {request_type}")
                continue
            gpt_response = json.loads(gpt_response_str)
        except json.JSONDecodeError:
            # Skip this response if JSON is not properly formatted
            logger.error(f"Error parsing GPT response for session_id: {session_id} and request_type: {request_type}")
            logger.error(f"Skipping this response for session_id: {session_id} and request_type: {request_type}")
            continue

        # Extract questions if they exist
        if 'questions' in gpt_response:
            # questions.append({request_type: gpt_response['questions']})
            questions.append({"request_type": request_type, "questions": gpt_response['questions']})

    logger.info(f"Extracted {len(questions)} questions from responses.")
    return questions


def create_interview_experience_messages(questions, n, role, experience_level):
    """
    Creates a structured message for generating interview experience questions
    based on the provided questions, number of questions, role, and experience level.
    Returns the formatted messages.
    """
    logger.info("Creating interview experience messages.")
    if experience_level == None or experience_level == "None":
        experience_level = ""
    # all_questions = ""	
    # for  question in questions:
    all_questions = ""
    categories = set()  # A set to hold unique categories

    # Identifying and categorizing each question
    for question in questions:
        request_type = question["request_type"]
        if request_type == "jd":
            categories.add("Job Description Based Questions")
            request_type =  "Job Description Based Questions"
        elif request_type == "resume":
            categories.add("Resume Based Questions")
            request_type = "Resume Based Questions"
        elif request_type == "job_title":
            categories.add("Job Title Based Questions")
            request_type = "Job Title Based Questions"
        # questions = "\n".join(question["questions"])
        # all_questions += f"{request_type}:\n{questions}\n\n"
        else:
            # Fallback for any unspecified request types
            formatted_type = f"{request_type.title().replace('_', ' ')} Based Questions"
            categories.add(formatted_type)
            request_type = formatted_type

        # Formatting questions into a string
        formatted_questions = "\n".join(question["questions"])
        all_questions += f"{request_type}:\n{formatted_questions}\n\n"

    # Constructing the categories sentence to be included in the prompt
    categories_list = ", ".join(categories)

    # print("All Questions: ", all_questions)
    #use following prompt to generate questions
    db_prompt = fetch_latest_prompt("interview_experience")
    db_prompt = db_prompt.format(n=n,experience_level=experience_level, role=role, categories_list = categories_list)

    # print("DB prompt: ", db_prompt)

    prompt = f""" {db_prompt}

    Questions:
    {all_questions}

    Response Generation Guidelines:
    Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering. 
    Dont output anything other than JSON not even markdown.
    """
    # print("Prompt: \n", prompt)
    logger.info("Interview experience messages created successfully.")
    try:
        messages = [ {"role": "system", "content": prompt}]
        messages.append({"role": "user", "content": "\nQuestions:\n"})
        return messages
    except Exception as e:
        logger.error("Exception: ", e)
        return None

