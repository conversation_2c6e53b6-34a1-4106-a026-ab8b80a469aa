2025-03-23 01:35:46,611 - api_logger - INFO - Request received for Job Title with Session ID: new-session-for-me Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 10
2025-03-23 01:35:52,740 - api_logger - INFO - Job Title Request with Session ID: new-session-for-me Successfully received and added to background task
2025-03-23 01:35:53,014 - api_logger - INFO - No previous session id
2025-03-23 01:36:02,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 01:36:02,354 - api_logger - INFO - Succesfully generated job title questions for session_id: new-session-for-me
2025-03-23 01:37:12,706 - api_logger - INFO - Request received for JD with Session ID: new-session-for-me Model: gpt-4o-mini and is_ca: False
2025-03-23 01:37:14,716 - api_logger - INFO - JD Request with Session ID: new-session-for-me Successfully received and added to background task
2025-03-23 01:37:22,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 01:38:08,567 - api_logger - INFO - Request received for Resume with Session ID: new-session-for-me Model: gpt-4o-mini and is_ca: False
2025-03-23 01:38:10,497 - api_logger - INFO - Resume Request with Session ID: new-session-for-me Successfully received and added to background task
2025-03-23 01:38:10,807 - api_logger - INFO - No previous session id found for session_id: new-session-for-me
2025-03-23 01:38:11,522 - api_logger - INFO - Successfully extracted resume text for session_id: new-session-for-me
2025-03-23 01:38:20,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 01:38:20,638 - api_logger - INFO - Succesfully generated resume questions for session_id: new-session-for-me
2025-03-23 01:40:36,293 - api_logger - INFO - Request received for Interview Experience with Session ID: new-session-for-me Model: gpt-4o-mini and is_ca: False
2025-03-23 01:40:38,009 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-session-for-me Model: gpt-4o-mini and is_ca: False
2025-03-23 01:40:38,286 - api_logger - INFO - Job status for session new-session-for-me: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-23 01:40:38,287 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-session-for-me
2025-03-23 01:40:38,897 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 10 for session_id: new-session-for-me
2025-03-23 01:40:44,225 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 01:40:44,907 - api_logger - INFO - Interview Experience Request with Session ID: new-session-for-me Successfully processed
2025-03-23 01:41:08,361 - api_logger - INFO - Fetching session data for session ID: new-session-for-me and is_ca: False
2025-03-23 01:41:10,857 - api_logger - INFO - Session data fetched successfully for session ID: new-session-for-me
2025-03-23 15:07:53,306 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:07:53,306 - api_logger - INFO - Processing PDF file.
2025-03-23 15:07:53,374 - api_logger - INFO - File text extraction completed.
2025-03-23 15:07:53,380 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-23 15:08:00,520 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:08:05,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:08:05,964 - api_logger - INFO - Response received successfully.
2025-03-23 15:08:05,977 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:08:05,977 - api_logger - INFO - Processing PDF file.
2025-03-23 15:08:06,193 - api_logger - INFO - File text extraction completed.
2025-03-23 15:08:06,193 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-23 15:08:08,816 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:08:10,653 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:08:10,662 - api_logger - INFO - Response received successfully.
2025-03-23 15:10:52,206 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:10:52,206 - api_logger - INFO - Processing PDF file.
2025-03-23 15:10:52,341 - api_logger - INFO - File text extraction completed.
2025-03-23 15:10:52,341 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-23 15:10:54,517 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:11:00,154 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:11:00,159 - api_logger - INFO - Response received successfully.
2025-03-23 15:11:00,168 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:11:00,168 - api_logger - INFO - Processing PDF file.
2025-03-23 15:11:00,574 - api_logger - INFO - File text extraction completed.
2025-03-23 15:11:00,581 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-23 15:11:02,633 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:11:04,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:11:04,837 - api_logger - INFO - Response received successfully.
2025-03-23 15:13:01,128 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:13:01,130 - api_logger - INFO - Processing PDF file.
2025-03-23 15:13:01,190 - api_logger - INFO - File text extraction completed.
2025-03-23 15:13:01,197 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-23 15:13:03,427 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:13:06,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:13:06,460 - api_logger - INFO - Response received successfully.
2025-03-23 15:15:17,851 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:15:17,851 - api_logger - INFO - Processing PDF file.
2025-03-23 15:15:17,981 - api_logger - INFO - File text extraction completed.
2025-03-23 15:15:17,985 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-23 15:15:20,430 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:15:23,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:15:23,768 - api_logger - INFO - Response received successfully.
2025-03-23 15:15:23,778 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:15:23,778 - api_logger - INFO - Processing PDF file.
2025-03-23 15:15:24,197 - api_logger - INFO - File text extraction completed.
2025-03-23 15:15:24,199 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-23 15:15:26,251 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:15:28,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:15:28,128 - api_logger - INFO - Response received successfully.
2025-03-23 15:16:21,274 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:16:21,277 - api_logger - INFO - Processing PDF file.
2025-03-23 15:16:21,419 - api_logger - INFO - File text extraction completed.
2025-03-23 15:16:21,421 - api_logger - INFO - Getting file text for uploaded file of type: application/pdf
2025-03-23 15:16:21,421 - api_logger - INFO - Processing PDF file.
2025-03-23 15:16:21,780 - api_logger - INFO - File text extraction completed.
2025-03-23 15:16:21,780 - api_logger - INFO - Generating response with model: gpt-3.5-turbo
2025-03-23 15:16:24,771 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-23 15:16:24,780 - api_logger - INFO - Response received successfully.
2025-03-23 15:21:49,166 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-23 15:21:52,065 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-23 15:21:57,420 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-23 15:22:05,251 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-23 15:22:08,163 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:22:16,215 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:22:16,427 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:22:44,999 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:22:45,200 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:22:46,833 - api_logger - INFO - Updating prompt for request_type: interview_experience
2025-03-23 15:22:53,682 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-23 15:22:56,886 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:23:06,180 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:23:06,396 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 15:23:08,475 - api_logger - INFO - Updating prompt for request_type: interview_experience
2025-03-23 15:23:14,228 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-23 15:23:17,223 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-23 17:41:53,029 - api_logger - INFO - Fetching session data for session ID: new-session and is_ca: False
2025-03-23 17:41:54,821 - api_logger - INFO - Fetching session data for session_id: new-session
2025-03-23 17:41:55,420 - api_logger - INFO - Session data fetched successfully for session ID: new-session
