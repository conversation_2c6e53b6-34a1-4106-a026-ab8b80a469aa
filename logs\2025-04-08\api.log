2025-04-08 10:53:30,755 - api_logger - INFO - Request received for JD with Session ID: new-test11 Model: gpt-4o-mini and is_ca: False
2025-04-08 10:53:38,893 - api_logger - ERROR - Exception: cannot access local variable 'jd_file_name' where it is not associated with a value Occurred while processing JD request
2025-04-08 10:53:38,906 - api_logger - INFO - Processing JD upload for session_id: new-test11
2025-04-08 10:53:38,906 - api_logger - INFO - Checking if session does not exist for session_id: new-test11, request_type: jd
2025-04-08 10:53:39,268 - api_logger - INFO - No previous session id
2025-04-08 10:53:39,268 - api_logger - INFO - Inserting status for session_id: new-test11, request_type: jd, status: Inprogress
2025-04-08 10:53:39,785 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-04-08 10:53:41,602 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-08 10:53:48,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-08 10:53:48,801 - api_logger - INFO - Response received successfully.
2025-04-08 10:53:48,801 - api_logger - INFO - Inserting record for session_id: new-test11, request_type: jd
2025-04-08 10:53:49,329 - api_logger - INFO - JD response generated for session_id: new-test11, response_time: 9.894671440124512s
2025-04-08 10:53:49,329 - api_logger - INFO - Updating status for session_id: new-test11, request_type: jd, status: complete
2025-04-08 10:53:49,878 - api_logger - INFO - Status updated successfully.
2025-04-08 10:57:40,889 - api_logger - INFO - Request received for JD with Session ID: new-test11 Model: gpt-4o-mini and is_ca: False
2025-04-08 10:57:42,382 - api_logger - INFO - JD Request with Session ID: new-test11 Successfully received and added to background task
2025-04-08 10:57:42,400 - api_logger - INFO - Processing JD upload for session_id: new-test11
2025-04-08 10:57:42,400 - api_logger - INFO - Checking if session does not exist for session_id: new-test11, request_type: jd
2025-04-08 10:57:42,740 - api_logger - INFO - Updating job description for session_id: new-test11
2025-04-08 10:57:43,864 - api_logger - INFO - Inserting status for session_id: new-test11, request_type: jd, status: Inprogress
2025-04-08 10:57:44,387 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-04-08 10:57:46,014 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-08 10:57:52,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-08 10:57:52,793 - api_logger - INFO - Response received successfully.
2025-04-08 10:57:52,801 - api_logger - INFO - Inserting record for session_id: new-test11, request_type: jd
2025-04-08 10:57:53,329 - api_logger - INFO - JD response generated for session_id: new-test11, response_time: 10.400678873062134s
2025-04-08 10:57:53,329 - api_logger - INFO - Updating status for session_id: new-test11, request_type: jd, status: complete
2025-04-08 10:57:53,842 - api_logger - INFO - Status updated successfully.
2025-04-08 10:58:15,215 - api_logger - INFO - Fetching session data for session ID: new-test11 and is_ca: False
2025-04-08 10:58:16,876 - api_logger - INFO - Fetching session data for session_id: new-test11
2025-04-08 10:58:17,242 - api_logger - INFO - Session data fetched successfully for session ID: new-test11
2025-04-08 10:58:29,406 - api_logger - INFO - Request received for JD with Session ID: new-test11 Model: gpt-4o-mini and is_ca: False
2025-04-08 10:58:31,174 - api_logger - INFO - JD Request with Session ID: new-test11 Successfully received and added to background task
2025-04-08 10:58:31,174 - api_logger - INFO - Processing JD upload for session_id: new-test11
2025-04-08 10:58:31,185 - api_logger - INFO - Checking if session does not exist for session_id: new-test11, request_type: jd
2025-04-08 10:58:31,487 - api_logger - INFO - Updating job description for session_id: new-test11
2025-04-08 10:58:32,749 - api_logger - INFO - Inserting status for session_id: new-test11, request_type: jd, status: Inprogress
2025-04-08 10:58:33,427 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-04-08 10:58:34,942 - api_logger - INFO - Fetching session data for session ID: new-test11 and is_ca: False
2025-04-08 10:58:35,086 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-08 10:58:36,810 - api_logger - INFO - Fetching session data for session_id: new-test11
2025-04-08 10:58:37,327 - api_logger - ERROR - Exception: 404: Session not found Occurred while fetching session data
2025-04-08 10:58:37,327 - api_logger - INFO - Session data fetched successfully for session ID: new-test11
2025-04-08 10:58:39,970 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-08 10:58:40,092 - api_logger - INFO - Response received successfully.
2025-04-08 10:58:40,092 - api_logger - INFO - Inserting record for session_id: new-test11, request_type: jd
2025-04-08 10:58:40,408 - api_logger - INFO - Fetching session data for session ID: new-test11 and is_ca: False
2025-04-08 10:58:40,635 - api_logger - INFO - JD response generated for session_id: new-test11, response_time: 8.906550407409668s
2025-04-08 10:58:40,635 - api_logger - INFO - Updating status for session_id: new-test11, request_type: jd, status: complete
2025-04-08 10:58:41,209 - api_logger - INFO - Status updated successfully.
2025-04-08 10:58:41,985 - api_logger - INFO - Fetching session data for session_id: new-test11
2025-04-08 10:58:42,284 - api_logger - INFO - Session data fetched successfully for session ID: new-test11
2025-04-08 11:00:56,147 - api_logger - INFO - Request received for JD with Session ID: new-test11 Model: gpt-4o-mini and is_ca: False
2025-04-08 11:00:57,971 - api_logger - INFO - Getting file text for: temp\uploaded_ds_jd.pdf
2025-04-08 11:00:57,988 - api_logger - INFO - Processing PDF file.
2025-04-08 11:00:58,422 - api_logger - INFO - File text extraction completed.
2025-04-08 11:00:58,422 - api_logger - INFO - JD Request with Session ID: new-test11 Successfully received and added to background task
2025-04-08 11:00:58,434 - api_logger - INFO - Processing JD upload for session_id: new-test11
2025-04-08 11:00:58,437 - api_logger - INFO - Checking if session does not exist for session_id: new-test11, request_type: jd
2025-04-08 11:00:58,687 - api_logger - INFO - Updating job description for session_id: new-test11
2025-04-08 11:00:59,957 - api_logger - INFO - Inserting status for session_id: new-test11, request_type: jd, status: Inprogress
2025-04-08 11:01:00,489 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-04-08 11:01:02,526 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-08 11:01:07,231 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-08 11:01:07,247 - api_logger - INFO - Response received successfully.
2025-04-08 11:01:07,263 - api_logger - INFO - Inserting record for session_id: new-test11, request_type: jd
2025-04-08 11:01:07,820 - api_logger - INFO - JD response generated for session_id: new-test11, response_time: 8.82551884651184s
2025-04-08 11:01:07,820 - api_logger - INFO - Updating status for session_id: new-test11, request_type: jd, status: complete
2025-04-08 11:01:08,464 - api_logger - INFO - Status updated successfully.
2025-04-08 11:02:16,802 - api_logger - INFO - Request received for JD with Session ID: new-test12 Model: gpt-4o-mini and is_ca: False
2025-04-08 11:02:18,464 - api_logger - INFO - JD Request with Session ID: new-test12 Successfully received and added to background task
2025-04-08 11:02:18,464 - api_logger - INFO - Processing JD upload for session_id: new-test12
2025-04-08 11:02:18,464 - api_logger - INFO - Checking if session does not exist for session_id: new-test12, request_type: jd
2025-04-08 11:02:18,723 - api_logger - INFO - No previous session id
2025-04-08 11:02:18,731 - api_logger - INFO - Inserting status for session_id: new-test12, request_type: jd, status: Inprogress
2025-04-08 11:02:19,283 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-04-08 11:02:20,974 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-08 11:02:24,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-08 11:02:24,852 - api_logger - INFO - Response received successfully.
2025-04-08 11:02:24,852 - api_logger - INFO - Inserting record for session_id: new-test12, request_type: jd
2025-04-08 11:02:25,385 - api_logger - INFO - JD response generated for session_id: new-test12, response_time: 6.388253211975098s
2025-04-08 11:02:25,385 - api_logger - INFO - Updating status for session_id: new-test12, request_type: jd, status: complete
2025-04-08 11:02:25,900 - api_logger - INFO - Status updated successfully.
2025-04-08 11:02:36,134 - api_logger - INFO - Fetching session data for session ID: new-test12 and is_ca: False
2025-04-08 11:02:38,375 - api_logger - INFO - Fetching session data for session_id: new-test12
2025-04-08 11:02:38,787 - api_logger - INFO - Session data fetched successfully for session ID: new-test12
