"""
Example: Complete Interview Experience with Audio Generation

This example demonstrates how to:
1. Generate interview questions using existing APIs
2. Generate audio for the first few questions
3. Monitor progress and download audio files
"""

import requests
import time
import json
import os
from dotenv import load_dotenv

load_dotenv()

# API Configuration
BASE_URL = "http://localhost:8001"
API_KEY = os.getenv("API_KEY_NAME", "myinterviewpractice")
API_SECRET = os.getenv("API_SECRET_NAME", "a0a408ef-e2b3-4680-bd58-35dbf66e1a7f")

headers = {
    "Content-Type": "application/json",
    "API_KEY": API_KEY,
    "API_SECRET": API_SECRET
}

def upload_job_title(session_id, job_title, experience_level, num_questions=5):
    """Upload job title and experience level."""
    print(f"📝 Uploading job title: {job_title}")

    params = {
        "session_id": session_id,
        "job_title": job_title,
        "number_of_questions": num_questions,
        "model": "gpt-4o-mini",
        "experience_level": experience_level,
        "is_ca": False
    }

    response = requests.post(
        f"{BASE_URL}/upload_job_title_experience_level/",
        params=params,
        headers=headers
    )

    if response.status_code == 200:
        print("✅ Job title uploaded successfully")
        return True
    else:
        print(f"❌ Failed to upload job title: {response.text}")
        return False

def get_interview_experience(session_id):
    """Get generated interview questions."""
    print("🎯 Retrieving interview experience...")

    params = {
        "session_id": session_id,
        "model": "gpt-4o-mini",
        "is_ca": False
    }

    # Wait a bit for background processing
    print("   Waiting for question generation...")
    time.sleep(10)

    response = requests.post(
        f"{BASE_URL}/get_interview_experience/",
        params=params,
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        if data.get("status"):
            print("✅ Interview experience retrieved successfully")
            return data
        else:
            print(f"❌ Interview generation failed: {data.get('error', 'Unknown error')}")
            return None
    else:
        print(f"❌ Failed to get interview experience: {response.text}")
        return None

def extract_questions_from_experience(experience_data):
    """Extract questions from interview experience response."""
    questions = []

    try:
        # The response structure may vary, adapt as needed
        if "interview_questions" in experience_data:
            for section in experience_data["interview_questions"]:
                if "questions" in section:
                    questions.extend(section["questions"])
        elif "questions" in experience_data:
            questions = experience_data["questions"]

        print(f"📋 Extracted {len(questions)} questions")
        return questions
    except Exception as e:
        print(f"❌ Error extracting questions: {str(e)}")
        return []

def generate_audio_for_questions(questions, voice_id="21m00Tcm4TlvDq8ikWAM", max_questions=3):
    """Generate audio for the first few questions."""
    print(f"🎵 Generating audio for first {max_questions} questions...")

    # Limit to first N questions for faster processing
    selected_questions = questions[:max_questions]

    request_data = {
        "questions": selected_questions,
        "voice_id": voice_id,
        "model": "eleven_flash_v2_5"
    }

    response = requests.post(
        f"{BASE_URL}/audio/generate-audio",
        json=request_data,
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        job_id = data["job_id"]
        print(f"✅ Audio generation started - Job ID: {job_id}")
        return job_id
    else:
        print(f"❌ Failed to start audio generation: {response.text}")
        return None

def wait_for_audio_completion(job_id, timeout=60):
    """Wait for audio generation to complete."""
    print("⏳ Waiting for audio generation to complete...")

    start_time = time.time()
    while time.time() - start_time < timeout:
        response = requests.get(f"{BASE_URL}/audio/job-status/{job_id}")

        if response.status_code == 200:
            data = response.json()
            status = data["status"]
            progress = data["progress"]

            print(f"   Progress: {progress}% - Status: {status}")

            if status in ["completed", "partial"]:
                print("✅ Audio generation completed!")
                return data["audio_files"]
            elif status == "failed":
                print(f"❌ Audio generation failed: {data.get('error_message')}")
                return None

            time.sleep(3)
        else:
            print(f"❌ Error checking status: {response.text}")
            return None

    print("⏰ Timeout waiting for audio completion")
    return None

def download_audio_files(job_id, audio_files, download_dir="downloaded_audio"):
    """Download all generated audio files."""
    print(f"⬇️  Downloading {len(audio_files)} audio files...")

    # Create download directory
    os.makedirs(download_dir, exist_ok=True)

    downloaded_files = []

    for audio_file in audio_files:
        filename = audio_file["file_url"].split("/")[-1]
        question_text = audio_file["question_text"][:50] + "..."

        print(f"   Downloading: {question_text}")

        response = requests.get(f"{BASE_URL}/audio/download/{job_id}/{filename}")

        if response.status_code == 200:
            local_filename = f"question_{audio_file['question_index'] + 1}.mp3"
            local_path = os.path.join(download_dir, local_filename)

            with open(local_path, "wb") as f:
                f.write(response.content)

            downloaded_files.append({
                "local_path": local_path,
                "question": audio_file["question_text"],
                "duration": audio_file["duration_seconds"]
            })

            print(f"   ✅ Saved: {local_path}")
        else:
            print(f"   ❌ Failed to download {filename}")

    return downloaded_files

def run_complete_interview_with_audio():
    """Run complete interview generation with audio."""
    print("🚀 Starting Complete Interview Experience with Audio")
    print("=" * 60)

    # Configuration
    session_id = f"demo_session_{int(time.time())}"
    job_title = "Senior Software Engineer"
    experience_level = "5"
    voice_id = "21m00Tcm4TlvDq8ikWAM"  # Rachel - Asian Women voice

    print(f"Session ID: {session_id}")
    print(f"Job Title: {job_title}")
    print(f"Experience Level: {experience_level} years")
    print(f"Voice: {voice_id}")
    print("-" * 60)

    # Step 1: Upload job title
    if not upload_job_title(session_id, job_title, experience_level):
        return

    # Step 2: Get interview experience
    experience_data = get_interview_experience(session_id)
    if not experience_data:
        return

    # Step 3: Extract questions
    questions = extract_questions_from_experience(experience_data)
    if not questions:
        print("❌ No questions found in interview experience")
        return

    print(f"\n📋 Generated Questions:")
    for i, question in enumerate(questions[:5], 1):  # Show first 5
        print(f"   {i}. {question}")
    if len(questions) > 5:
        print(f"   ... and {len(questions) - 5} more questions")

    # Step 4: Generate audio for first 3 questions
    audio_job_id = generate_audio_for_questions(questions, voice_id, max_questions=3)
    if not audio_job_id:
        return

    # Step 5: Wait for audio completion
    audio_files = wait_for_audio_completion(audio_job_id)
    if not audio_files:
        return

    # Step 6: Download audio files
    downloaded_files = download_audio_files(audio_job_id, audio_files)

    # Step 7: Summary
    print("\n🎉 Interview Experience with Audio Complete!")
    print("=" * 60)
    print(f"✅ Generated {len(questions)} interview questions")
    print(f"✅ Created {len(audio_files)} audio files")
    print(f"✅ Downloaded {len(downloaded_files)} audio files")

    print("\n📁 Downloaded Audio Files:")
    for file_info in downloaded_files:
        duration = file_info["duration"]
        print(f"   🎵 {file_info['local_path']} ({duration:.1f}s)")
        print(f"      Question: {file_info['question'][:80]}...")

    print(f"\n🔗 Audio Job ID: {audio_job_id}")
    print("   Use this ID to download individual files or check status")

    return {
        "session_id": session_id,
        "questions": questions,
        "audio_job_id": audio_job_id,
        "audio_files": audio_files,
        "downloaded_files": downloaded_files
    }

if __name__ == "__main__":
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/audio/voices")
        if response.status_code == 200:
            result = run_complete_interview_with_audio()
            if result:
                print("\n💡 Next Steps:")
                print("   1. Play the downloaded audio files")
                print("   2. Use the remaining questions for practice")
                print("   3. Generate audio for more questions if needed")
        else:
            print("❌ Audio API not responding. Please check if the server is running.")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start the FastAPI server first:")
        print("   uvicorn api.main:app --reload")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
