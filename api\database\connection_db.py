"""
This module provides functions to establish a connection to a MySQL database.
It supports connections to different databases based on the specified parameters.
"""

import pymysql
import os
from dotenv import load_dotenv
load_dotenv()

def get_connection(is_ca):
    """
    Establishes a connection to the MySQL database.
    
    Parameters:
    - is_ca (bool): Indicates whether to connect to the Canada database.

    Returns:
    - connection: A pymysql connection object.
    """
    host=os.getenv("mysql_host")
    database=os.getenv("mysql_database")
    database_ca = os.getenv("mysql_database_ca")
    user=os.getenv("mysql_user")
    password=os.getenv("mysql_password")    
    # Replace these values with your database connection details
    if is_ca:
        use_db = database_ca
    else:
        use_db = database
    connection = pymysql.connect(host=host,
                                 user=user,
                                 password=password,
                                 database=use_db,
                                 cursorclass=pymysql.cursors.DictCursor)
    return connection
