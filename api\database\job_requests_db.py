"""
This module provides functions to interact with the job requests database.
It includes functionalities to fetch role and experience levels, 
and to update job experience, job descriptions, and resumes for a given session ID.
"""
from api.common.api_logger import api_logger as logger

def get_role_experience_level(session_id, connection):
    """
    Fetches the role and experience level for a given session ID.

    Parameters:
    - session_id (str): The session ID.
    - connection: The database connection object.

    Returns:
    - result: A list of dictionaries containing role and experience level.
    """
    try:
        logger.info(f"Fetching role and experience level for session_id: {session_id}")
        with connection.cursor() as cursor:
            # SQL query to select records by session_id
            sql = "SELECT role, experience_level, number_of_questions FROM requests WHERE session_id = %s and request_type = 'job_title'"
            cursor.execute(sql, (session_id,))

            # Fetch all the matching records
            result = cursor.fetchall()
            logger.info(f"SQL result: {result}")
            return result
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        return None

def update_job_experience(session_id, connection):
    """
    Updates job experience for a given session ID by deleting related entries.

    Parameters:
    - session_id (str): The session ID.
    - connection: The database connection object.
    """
    try:
        logger.info(f"Updating job experience for session_id: {session_id}")
        with connection.cursor() as cursor:

            # Delete entries from the 'jobs' table
            cursor.execute("DELETE FROM jobs WHERE session_id = %s AND request_type IN (%s, %s, %s)", (session_id, "job_title", "jd", "resume"))

            # Delete entries from the 'requests' table
            cursor.execute("DELETE FROM requests WHERE session_id = %s and request_type in (%s, %s, %s)", (session_id, "job_title","jd", "resume"))

        # Commit the transaction
        connection.commit()

    except Exception as e:
        logger.error("Exception during database modification:", e)

def update_jd(session_id, connection):
    """
    Updates the job description for a given session ID by deleting related entries.

    Parameters:
    - session_id (str): The session ID.
    - connection: The database connection object.
    """
    try:
        logger.info(f"Updating job description for session_id: {session_id}")
        # Start a transaction
        connection.begin()

        with connection.cursor() as cursor:

            # Delete entries from the 'jobs' table
            cursor.execute("DELETE FROM jobs WHERE session_id = %s AND request_type IN ( %s, %s)", (session_id, "jd", "resume"))

            # Delete entries from the 'requests' table
            cursor.execute("DELETE FROM requests WHERE session_id = %s and request_type in (%s, %s)", (session_id,"jd", "resume"))

        # Commit the transaction
        connection.commit()

    except Exception as e:
        logger.error("Exception during database modification:", e)

        # Rollback the transaction on error
        if connection:
            connection.rollback()

def update_resume(session_id, connection):
    """
    Updates the resume for a given session ID by deleting related entries.

    Parameters:
    - session_id (str): The session ID.
    - connection: The database connection object.
    """
    try:
        logger.info(f"Updating resume for session_id: {session_id}")
        # Start a transaction
        connection.begin()

        with connection.cursor() as cursor:

            # Delete entries from the 'jobs' table
            cursor.execute("DELETE FROM jobs WHERE session_id = %s AND request_type = %s", (session_id,  "resume"))

            # Delete entries from the 'requests' table
            cursor.execute("DELETE FROM requests WHERE session_id = %s and request_type = %s", (session_id, "resume"))

        # Commit the transaction
        connection.commit()

    except Exception as e:
        logger.error("Exception during database modification:", e)

        # Rollback the transaction on error
        if connection:
            connection.rollback()


