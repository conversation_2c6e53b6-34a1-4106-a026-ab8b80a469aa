2025-03-24 13:36:14,429 - api_logger - INFO - Request received for Job Title with Session ID: testing Model: gpt-3.5-turbo and is_ca: False and Experience Level: None and Number of Questions: 10
2025-03-24 13:36:21,274 - api_logger - INFO - Job Title Request with Session ID: testing Successfully received and added to background task
2025-03-24 13:36:21,274 - api_logger - INFO - Starting background response generation for session_id: testing, job_title: data scientist
2025-03-24 13:36:21,281 - api_logger - INFO - Checking if session does not exist for session_id: testing, request_type: job_title
2025-03-24 13:36:21,532 - api_logger - INFO - Updating job experience for session_id: testing
2025-03-24 13:36:22,382 - api_logger - INFO - Inserting status for session_id: testing, request_type: job_title, status: Inprogress
2025-03-24 13:36:22,904 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 13:36:24,762 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 13:36:30,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 13:36:30,353 - api_logger - INFO - Response received successfully.
2025-03-24 13:36:30,353 - api_logger - INFO - Succesfully generated job title questions for session_id: testing
2025-03-24 13:36:30,353 - api_logger - INFO - Inserting record for session_id: testing, request_type: job_title
2025-03-24 13:36:30,955 - api_logger - INFO - Inserted initial status for session_id: testing
2025-03-24 13:36:30,955 - api_logger - INFO - Updating status for session_id: testing, request_type: job_title, status: complete
2025-03-24 13:36:31,593 - api_logger - INFO - Status updated successfully.
2025-03-24 13:36:31,593 - api_logger - INFO - Response generated for session_id: testing, response_time: 9ms
2025-03-24 13:38:16,045 - api_logger - INFO - Request received for Interview Experience with Session ID: testing Model: gpt-3.5-turbo and is_ca: False
2025-03-24 13:38:17,687 - api_logger - INFO - Processing request for Interview Experience with Session ID: testing Model: gpt-4o-mini and is_ca: False
2025-03-24 13:38:17,687 - api_logger - INFO - Checking if all tasks are complete for session_id: testing
2025-03-24 13:38:17,933 - api_logger - INFO - Job status for session testing: [{'status': 'complete', 'request_type': 'job_title'}]
2025-03-24 13:38:17,935 - api_logger - INFO - Proceeding with tasks completion status for session ID: testing
2025-03-24 13:38:17,935 - api_logger - INFO - Processing interview for session_id: testing
2025-03-24 13:38:17,935 - api_logger - INFO - Fetching requests for session_id: testing
2025-03-24 13:38:18,213 - api_logger - INFO - Extracting questions from responses for session_id: testing
2025-03-24 13:38:18,213 - api_logger - INFO - Extracted 1 questions from responses.
2025-03-24 13:38:18,213 - api_logger - INFO - Fetching role and experience level for session_id: testing
2025-03-24 13:38:18,561 - api_logger - INFO - Role: data scientist, Experience Level: None, Number of Questions: 10 for session_id: testing
2025-03-24 13:38:18,561 - api_logger - INFO - Creating interview experience messages.
2025-03-24 13:38:18,562 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 13:38:20,244 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 13:38:20,244 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 13:38:23,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 13:38:24,001 - api_logger - INFO - Response received successfully.
2025-03-24 13:38:24,002 - api_logger - INFO - Inserting record for session_id: testing, request_type: interview_experience
2025-03-24 13:38:24,517 - api_logger - INFO - Extracted questions for session_id: testing: [{'request_type': 'job_title', 'questions': ['What programming languages and tools do you commonly use for data analysis, and why do you prefer them?', "Can you describe your experience with statistical analysis and how you've applied it in your previous projects?", "What methodologies do you use for developing predictive models, and can you provide an example of a successful model you've implemented?", 'How do you approach feature selection and engineering in your data science projects?', 'Can you explain how you handle missing data and what strategies you find most effective?', 'Describe your experience with machine learning algorithms. Which algorithms have you worked with, and how did you choose the appropriate one for your project?', 'How do you evaluate the performance of a machine learning model?', 'What is your experience with data visualization, and which tools do you prefer for presenting your findings?', 'Can you talk about a time you used A/B testing or experimental design in your work?', 'How do you ensure your insights and recommendations are communicated effectively to non-technical stakeholders?', 'What is your experience with big data technologies, such as Hadoop or Spark?', 'How have you collaborated with cross-functional teams in your previous roles, and what role did you play in those collaborations?', 'Could you explain a situation where you faced a significant challenge in a data project? What was the challenge, and how did you overcome it?', 'Describe a situation where you had to prioritize competing tasks related to data projects, and how did you manage your time and resources effectively?', 'How do you approach training or mentoring team members who may not be as proficient in data science skills?', 'What is your experience with cloud-based data platforms, and how have you utilized them in your work?']}]
2025-03-24 13:38:24,517 - api_logger - INFO - Interview Experience Request with Session ID: testing Successfully processed
2025-03-24 13:39:44,588 - api_logger - INFO - Fetching session data for session ID: testing and is_ca: False
2025-03-24 13:39:46,244 - api_logger - INFO - Fetching session data for session_id: testing
2025-03-24 13:39:46,632 - api_logger - INFO - Session data fetched successfully for session ID: testing
2025-03-24 13:47:44,768 - api_logger - INFO - Fetching prompt for request type: RequestType.job_title
2025-03-24 13:47:44,769 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 13:47:46,810 - api_logger - INFO - Prompt retrieved successfully.
2025-03-24 15:24:42,241 - api_logger - INFO - Fetching prompt for request type: RequestType.interview_experience
2025-03-24 15:24:42,241 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 15:24:44,432 - api_logger - INFO - Prompt retrieved successfully.
2025-03-24 15:26:02,112 - api_logger - INFO - Updating prompt for request type: RequestType.interview_experience
2025-03-24 15:26:02,126 - api_logger - ERROR - Validation failed: Missing placeholder(s): {categories_list}
2025-03-24 15:26:16,637 - api_logger - INFO - Updating prompt for request type: RequestType.interview_experience
2025-03-24 15:26:16,637 - api_logger - ERROR - Validation failed: Missing placeholder(s): {role}, {categories_list}
2025-03-24 15:26:32,908 - api_logger - INFO - Updating prompt for request type: RequestType.interview_experience
2025-03-24 15:26:32,908 - api_logger - INFO - Updating prompt for request_type: RequestType.interview_experience
2025-03-24 15:26:35,382 - api_logger - INFO - Prompt updated successfully.
2025-03-24 19:12:34,046 - api_logger - INFO - Request received for Job Title with Session ID: new-test Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 19:12:40,904 - api_logger - INFO - Job Title Request with Session ID: new-test Successfully received and added to background task
2025-03-24 19:12:40,904 - api_logger - INFO - Starting background response generation for session_id: new-test, job_title: Data Scientist
2025-03-24 19:12:40,904 - api_logger - INFO - Checking if session does not exist for session_id: new-test, request_type: job_title
2025-03-24 19:12:41,219 - api_logger - INFO - No previous session id
2025-03-24 19:12:41,219 - api_logger - INFO - Inserting status for session_id: new-test, request_type: job_title, status: Inprogress
2025-03-24 19:12:41,833 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 19:12:43,683 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:12:46,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:12:46,603 - api_logger - INFO - Response received successfully.
2025-03-24 19:12:46,603 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test
2025-03-24 19:12:46,604 - api_logger - INFO - Inserting record for session_id: new-test, request_type: job_title
2025-03-24 19:12:47,116 - api_logger - INFO - Inserted initial status for session_id: new-test
2025-03-24 19:12:47,122 - api_logger - INFO - Updating status for session_id: new-test, request_type: job_title, status: complete
2025-03-24 19:12:47,665 - api_logger - INFO - Status updated successfully.
2025-03-24 19:12:47,672 - api_logger - INFO - Response generated for session_id: new-test, response_time: 5ms
2025-03-24 19:13:29,489 - api_logger - INFO - Fetching session data for session ID: new-test and is_ca: False
2025-03-24 19:13:31,007 - api_logger - INFO - Fetching session data for session_id: new-test
2025-03-24 19:13:31,307 - api_logger - INFO - Session data fetched successfully for session ID: new-test
2025-03-24 19:15:14,624 - api_logger - INFO - Request received for JD with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:15:16,244 - api_logger - INFO - JD Request with Session ID: new-test Successfully received and added to background task
2025-03-24 19:15:16,246 - api_logger - INFO - Processing JD upload for session_id: new-test
2025-03-24 19:15:16,246 - api_logger - INFO - Checking if session does not exist for session_id: new-test, request_type: jd
2025-03-24 19:15:16,605 - api_logger - INFO - No previous session id
2025-03-24 19:15:16,605 - api_logger - INFO - Inserting status for session_id: new-test, request_type: jd, status: Inprogress
2025-03-24 19:15:17,123 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 19:15:18,753 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:15:22,383 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:15:22,383 - api_logger - INFO - Response received successfully.
2025-03-24 19:15:22,392 - api_logger - INFO - Inserting record for session_id: new-test, request_type: jd
2025-03-24 19:15:23,085 - api_logger - INFO - JD response generated for session_id: new-test, response_time: 6.146227598190308s
2025-03-24 19:15:23,085 - api_logger - INFO - Updating status for session_id: new-test, request_type: jd, status: complete
2025-03-24 19:15:23,674 - api_logger - INFO - Status updated successfully.
2025-03-24 19:16:40,458 - api_logger - INFO - Request received for Resume with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:16:42,180 - api_logger - ERROR - Exception: [Errno 2] No such file or directory: 'data\\uploaded_Resume_Ved_Vekhande_IIITV.pdf' Occurred while processing resume request
2025-03-24 19:17:08,258 - api_logger - INFO - Request received for Resume with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:17:10,184 - api_logger - ERROR - Exception: [Errno 2] No such file or directory: 'data\\uploaded_Resume_Ved_Vekhande_IIITV.pdf' Occurred while processing resume request
2025-03-24 19:17:25,568 - api_logger - INFO - Request received for Resume with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:17:27,333 - api_logger - INFO - Resume Request with Session ID: new-test Successfully received and added to background task
2025-03-24 19:17:27,339 - api_logger - INFO - Processing resume for session_id: new-test
2025-03-24 19:17:27,339 - api_logger - INFO - Checking if session does not exist for session_id: new-test, request_type: resume
2025-03-24 19:17:27,643 - api_logger - INFO - No previous session id found for session_id: new-test
2025-03-24 19:17:27,643 - api_logger - INFO - Inserting status for session_id: new-test, request_type: resume, status: Inprogress
2025-03-24 19:17:28,223 - api_logger - INFO - Getting file text for: data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 19:17:28,266 - api_logger - INFO - Processing PDF file.
2025-03-24 19:17:28,333 - api_logger - INFO - File text extraction completed.
2025-03-24 19:17:28,333 - api_logger - INFO - Successfully extracted resume text for session_id: new-test
2025-03-24 19:17:28,333 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 19:17:30,083 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:17:33,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:17:33,642 - api_logger - INFO - Response received successfully.
2025-03-24 19:17:33,643 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test
2025-03-24 19:17:33,643 - api_logger - INFO - Inserting record for session_id: new-test, request_type: resume
2025-03-24 19:17:34,254 - api_logger - INFO - Updating status for session_id: new-test, request_type: resume, status: complete
2025-03-24 19:17:34,760 - api_logger - INFO - Status updated successfully.
2025-03-24 19:17:34,760 - api_logger - INFO - Resume response generated for session_id: new-test, response_time: 6ms
2025-03-24 19:18:01,723 - api_logger - INFO - Fetching session data for session ID: new-test and is_ca: False
2025-03-24 19:18:03,415 - api_logger - INFO - Fetching session data for session_id: new-test
2025-03-24 19:18:03,748 - api_logger - INFO - Session data fetched successfully for session ID: new-test
2025-03-24 19:18:31,865 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:18:33,443 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:18:33,443 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:18:33,713 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:18:33,713 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:18:33,713 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:18:33,713 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:18:34,004 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:18:34,004 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: job_title
2025-03-24 19:18:34,004 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: job_title
2025-03-24 19:18:34,004 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: jd
2025-03-24 19:18:34,004 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: jd
2025-03-24 19:18:34,004 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: resume
2025-03-24 19:18:34,004 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: resume
2025-03-24 19:18:34,004 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 19:18:34,004 - api_logger - INFO - Fetching role and experience level for session_id: new-test
2025-03-24 19:18:34,308 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test
2025-03-24 19:18:34,308 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:18:34,308 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:18:36,034 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:18:36,034 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:18:39,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:18:39,327 - api_logger - INFO - Response received successfully.
2025-03-24 19:18:39,327 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:19:52,689 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:19:54,382 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:19:54,383 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:19:54,644 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:19:54,644 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:19:54,644 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:19:54,644 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:19:54,932 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:19:54,933 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: job_title
2025-03-24 19:19:54,933 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: job_title
2025-03-24 19:19:54,933 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: jd
2025-03-24 19:19:54,933 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: jd
2025-03-24 19:19:54,933 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: resume
2025-03-24 19:19:54,933 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: resume
2025-03-24 19:19:54,933 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 19:19:54,933 - api_logger - INFO - Fetching role and experience level for session_id: new-test
2025-03-24 19:19:55,233 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test
2025-03-24 19:19:55,233 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:19:55,234 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:19:56,635 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:19:56,635 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:19:59,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:19:59,974 - api_logger - INFO - Response received successfully.
2025-03-24 19:19:59,974 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:21:57,740 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:21:59,962 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:21:59,963 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:22:00,314 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:22:00,322 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:22:00,323 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:22:00,323 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:22:00,833 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:22:00,833 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: job_title
2025-03-24 19:22:00,834 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: job_title
2025-03-24 19:22:00,835 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: jd
2025-03-24 19:22:00,835 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: jd
2025-03-24 19:22:00,837 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: resume
2025-03-24 19:22:00,837 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: resume
2025-03-24 19:22:00,837 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 19:22:00,837 - api_logger - INFO - Fetching role and experience level for session_id: new-test
2025-03-24 19:22:01,158 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test
2025-03-24 19:22:01,158 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:22:01,163 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:22:02,666 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:22:02,666 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:22:06,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:22:06,703 - api_logger - INFO - Response received successfully.
2025-03-24 19:22:06,703 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:22:36,303 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:22:37,753 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:22:37,755 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:22:38,034 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:22:38,034 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:22:38,034 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:22:38,034 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:22:38,302 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:22:38,303 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: job_title
2025-03-24 19:22:38,304 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: job_title
2025-03-24 19:22:38,304 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: jd
2025-03-24 19:22:38,305 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: jd
2025-03-24 19:22:38,305 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: resume
2025-03-24 19:22:38,306 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: resume
2025-03-24 19:22:38,306 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 19:22:38,306 - api_logger - INFO - Fetching role and experience level for session_id: new-test
2025-03-24 19:22:38,574 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test
2025-03-24 19:22:38,574 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:22:38,574 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:22:40,313 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:22:40,313 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:22:44,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:22:44,334 - api_logger - INFO - Response received successfully.
2025-03-24 19:22:44,335 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:27:03,399 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:27:05,143 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:27:05,143 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:27:05,472 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:27:05,473 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:27:05,474 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:27:05,474 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:27:05,773 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:27:05,775 - api_logger - ERROR - Exception: invalid syntax (<unknown>, line 1) Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:29:01,639 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:29:03,289 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:29:03,290 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:29:03,542 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:29:03,543 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:29:03,543 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:29:03,543 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:29:03,824 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:29:03,826 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: job_title
2025-03-24 19:29:03,826 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: job_title
2025-03-24 19:29:03,826 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: jd
2025-03-24 19:29:03,826 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: jd
2025-03-24 19:29:03,831 - api_logger - ERROR - Error parsing GPT response for session_id: new-test and request_type: resume
2025-03-24 19:29:03,832 - api_logger - ERROR - Skipping this response for session_id: new-test and request_type: resume
2025-03-24 19:29:03,833 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 19:29:03,833 - api_logger - INFO - Fetching role and experience level for session_id: new-test
2025-03-24 19:29:04,133 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test
2025-03-24 19:29:04,134 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:29:04,136 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:29:05,663 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:29:05,664 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:29:08,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:29:08,996 - api_logger - INFO - Response received successfully.
2025-03-24 19:29:08,996 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:36:07,217 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:36:10,133 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test Model: gpt-4o-mini and is_ca: False
2025-03-24 19:36:10,134 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test
2025-03-24 19:36:10,443 - api_logger - INFO - Job status for session new-test: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:36:10,443 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test
2025-03-24 19:36:10,443 - api_logger - INFO - Processing interview for session_id: new-test
2025-03-24 19:36:10,443 - api_logger - INFO - Fetching requests for session_id: new-test
2025-03-24 19:36:10,753 - api_logger - INFO - Extracting questions from responses for session_id: new-test
2025-03-24 19:36:10,754 - api_logger - INFO - Response str: questions=['What are the main themes explored in the text?', 'How does the author develop the characters throughout the story?', 'What is the significance of the setting in the narrative?', 'How does the plot structure contribute to the overall message of the work?', 'What literary devices are used to enhance the storytelling?', 'In what ways does the text reflect the historical context in which it was written?', 'How do the conflicts in the story drive the character development?', 'What role does symbolism play in the narrative?', "How does the point of view affect the reader's understanding of the story?", 'What are the moral or ethical dilemmas presented in the text?']
2025-03-24 19:36:10,754 - api_logger - INFO - Request type: job_title
2025-03-24 19:36:10,754 - api_logger - INFO - Response str: questions=['What are the main themes explored in the text?', 'How does the author develop the characters throughout the story?', 'What is the significance of the setting in the narrative?', 'Can you identify any symbols used in the text and their meanings?', 'How does the plot structure contribute to the overall impact of the story?', "What are the motivations behind the protagonist's actions?", 'How does the author use language and style to enhance the story?', 'What conflicts arise in the narrative, and how are they resolved?', 'In what ways does the text reflect the historical or cultural context in which it was written?', 'What are the key takeaways or messages from the text?']
2025-03-24 19:36:10,754 - api_logger - INFO - Request type: jd
2025-03-24 19:36:10,754 - api_logger - INFO - Response str: questions=['What are the main themes explored in the text?', 'How does the author develop the characters throughout the story?', 'What is the significance of the setting in the narrative?', 'Can you identify any symbols used in the text and their meanings?', 'How does the plot structure contribute to the overall impact of the story?', "What are the motivations behind the protagonist's actions?", 'How does the author use language and style to enhance the story?', 'What conflicts arise in the narrative, and how are they resolved?', 'In what ways does the text reflect the historical or cultural context in which it was written?', 'What are the key takeaways or messages from the text?']
2025-03-24 19:36:10,754 - api_logger - INFO - Request type: resume
2025-03-24 19:36:10,754 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 19:36:10,754 - api_logger - INFO - Fetching role and experience level for session_id: new-test
2025-03-24 19:36:11,069 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test
2025-03-24 19:36:11,069 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:36:11,069 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:36:12,897 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:36:12,897 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:36:17,099 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:36:17,135 - api_logger - INFO - Response received successfully.
2025-03-24 19:36:17,135 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test
2025-03-24 19:38:34,723 - api_logger - INFO - Request received for Job Title with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 19:38:38,003 - api_logger - INFO - Job Title Request with Session ID: new-test1 Successfully received and added to background task
2025-03-24 19:38:38,003 - api_logger - INFO - Starting background response generation for session_id: new-test1, job_title: Data Scientist
2025-03-24 19:38:38,003 - api_logger - INFO - Checking if session does not exist for session_id: new-test1, request_type: job_title
2025-03-24 19:38:38,513 - api_logger - INFO - No previous session id
2025-03-24 19:38:38,513 - api_logger - INFO - Inserting status for session_id: new-test1, request_type: job_title, status: Inprogress
2025-03-24 19:38:40,152 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 19:38:42,197 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:38:48,444 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:38:48,483 - api_logger - INFO - Response received successfully.
2025-03-24 19:38:48,483 - api_logger - ERROR - Error occurred: Object of type QuestionList is not JSON serializable
2025-03-24 19:38:48,483 - api_logger - ERROR - GPT error for session_id: new-test1 during job_title generation
2025-03-24 19:38:48,483 - api_logger - INFO - Inserting record for session_id: new-test1, request_type: job_title
2025-03-24 19:38:49,154 - api_logger - INFO - Inserted initial status for session_id: new-test1
2025-03-24 19:38:49,154 - api_logger - INFO - Updating status for session_id: new-test1, request_type: job_title, status: complete
2025-03-24 19:38:49,908 - api_logger - INFO - Status updated successfully.
2025-03-24 19:38:49,908 - api_logger - INFO - Response generated for session_id: new-test1, response_time: 10ms
2025-03-24 19:39:30,832 - api_logger - INFO - Request received for Job Title with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 19:39:33,185 - api_logger - INFO - Job Title Request with Session ID: new-test1 Successfully received and added to background task
2025-03-24 19:39:33,195 - api_logger - INFO - Starting background response generation for session_id: new-test1, job_title: Data Scientist
2025-03-24 19:39:33,195 - api_logger - INFO - Checking if session does not exist for session_id: new-test1, request_type: job_title
2025-03-24 19:39:33,607 - api_logger - INFO - Updating job experience for session_id: new-test1
2025-03-24 19:39:34,833 - api_logger - INFO - Inserting status for session_id: new-test1, request_type: job_title, status: Inprogress
2025-03-24 19:39:35,552 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 19:39:38,552 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:39:43,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:39:43,207 - api_logger - INFO - Response received successfully.
2025-03-24 19:39:43,207 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test1
2025-03-24 19:39:43,213 - api_logger - INFO - Inserting record for session_id: new-test1, request_type: job_title
2025-03-24 19:39:43,943 - api_logger - INFO - Inserted initial status for session_id: new-test1
2025-03-24 19:39:43,944 - api_logger - INFO - Updating status for session_id: new-test1, request_type: job_title, status: complete
2025-03-24 19:39:44,554 - api_logger - INFO - Status updated successfully.
2025-03-24 19:39:44,559 - api_logger - INFO - Response generated for session_id: new-test1, response_time: 10ms
2025-03-24 19:40:12,845 - api_logger - INFO - Request received for Job Title with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 19:40:15,294 - api_logger - INFO - Job Title Request with Session ID: new-test1 Successfully received and added to background task
2025-03-24 19:40:15,296 - api_logger - INFO - Starting background response generation for session_id: new-test1, job_title: Data Scientist
2025-03-24 19:40:15,296 - api_logger - INFO - Checking if session does not exist for session_id: new-test1, request_type: job_title
2025-03-24 19:40:15,594 - api_logger - INFO - Updating job experience for session_id: new-test1
2025-03-24 19:40:16,916 - api_logger - INFO - Inserting status for session_id: new-test1, request_type: job_title, status: Inprogress
2025-03-24 19:40:17,537 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 19:40:19,373 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:40:24,192 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:40:24,247 - api_logger - INFO - Response received successfully.
2025-03-24 19:40:24,247 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test1
2025-03-24 19:40:24,248 - api_logger - INFO - Inserting record for session_id: new-test1, request_type: job_title
2025-03-24 19:40:25,026 - api_logger - INFO - Inserted initial status for session_id: new-test1
2025-03-24 19:40:25,026 - api_logger - INFO - Updating status for session_id: new-test1, request_type: job_title, status: complete
2025-03-24 19:40:25,853 - api_logger - INFO - Status updated successfully.
2025-03-24 19:40:25,854 - api_logger - INFO - Response generated for session_id: new-test1, response_time: 8ms
2025-03-24 19:40:53,483 - api_logger - INFO - Request received for JD with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False
2025-03-24 19:40:56,245 - api_logger - INFO - JD Request with Session ID: new-test1 Successfully received and added to background task
2025-03-24 19:40:56,253 - api_logger - INFO - Processing JD upload for session_id: new-test1
2025-03-24 19:40:56,253 - api_logger - INFO - Checking if session does not exist for session_id: new-test1, request_type: jd
2025-03-24 19:40:56,543 - api_logger - INFO - No previous session id
2025-03-24 19:40:56,543 - api_logger - INFO - Inserting status for session_id: new-test1, request_type: jd, status: Inprogress
2025-03-24 19:40:57,154 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 19:40:59,134 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:41:02,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:41:02,803 - api_logger - INFO - Response received successfully.
2025-03-24 19:41:02,804 - api_logger - INFO - Inserting record for session_id: new-test1, request_type: jd
2025-03-24 19:41:03,733 - api_logger - INFO - JD response generated for session_id: new-test1, response_time: 6.551393508911133s
2025-03-24 19:41:03,733 - api_logger - INFO - Updating status for session_id: new-test1, request_type: jd, status: complete
2025-03-24 19:41:04,433 - api_logger - INFO - Status updated successfully.
2025-03-24 19:41:08,487 - api_logger - INFO - Request received for Resume with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False
2025-03-24 19:41:10,903 - api_logger - INFO - Resume Request with Session ID: new-test1 Successfully received and added to background task
2025-03-24 19:41:10,907 - api_logger - INFO - Processing resume for session_id: new-test1
2025-03-24 19:41:10,907 - api_logger - INFO - Checking if session does not exist for session_id: new-test1, request_type: resume
2025-03-24 19:41:11,213 - api_logger - INFO - No previous session id found for session_id: new-test1
2025-03-24 19:41:11,214 - api_logger - INFO - Inserting status for session_id: new-test1, request_type: resume, status: Inprogress
2025-03-24 19:41:11,903 - api_logger - INFO - Getting file text for: data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 19:41:11,934 - api_logger - INFO - Processing PDF file.
2025-03-24 19:41:12,088 - api_logger - INFO - File text extraction completed.
2025-03-24 19:41:12,088 - api_logger - INFO - Successfully extracted resume text for session_id: new-test1
2025-03-24 19:41:12,092 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 19:41:14,363 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:41:18,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:41:18,605 - api_logger - INFO - Response received successfully.
2025-03-24 19:41:18,605 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test1
2025-03-24 19:41:18,605 - api_logger - INFO - Inserting record for session_id: new-test1, request_type: resume
2025-03-24 19:41:19,392 - api_logger - INFO - Updating status for session_id: new-test1, request_type: resume, status: complete
2025-03-24 19:41:20,122 - api_logger - INFO - Status updated successfully.
2025-03-24 19:41:20,123 - api_logger - INFO - Resume response generated for session_id: new-test1, response_time: 7ms
2025-03-24 19:42:18,814 - api_logger - INFO - Fetching session data for session ID: new-test1 and is_ca: False
2025-03-24 19:42:21,433 - api_logger - INFO - Fetching session data for session_id: new-test1
2025-03-24 19:42:21,788 - api_logger - INFO - Session data fetched successfully for session ID: new-test1
2025-03-24 19:42:38,203 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False
2025-03-24 19:42:51,435 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test1 Model: gpt-4o-mini and is_ca: False
2025-03-24 19:42:51,437 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test1
2025-03-24 19:42:51,754 - api_logger - INFO - Job status for session new-test1: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 19:42:51,754 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test1
2025-03-24 19:42:51,754 - api_logger - INFO - Processing interview for session_id: new-test1
2025-03-24 19:42:51,754 - api_logger - INFO - Fetching requests for session_id: new-test1
2025-03-24 19:42:52,154 - api_logger - INFO - Extracting questions from responses for session_id: new-test1
2025-03-24 19:42:52,154 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 19:42:52,154 - api_logger - INFO - Fetching role and experience level for session_id: new-test1
2025-03-24 19:42:52,584 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test1
2025-03-24 19:42:52,584 - api_logger - INFO - Creating interview experience messages.
2025-03-24 19:42:52,584 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 19:42:55,232 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 19:42:55,233 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:42:59,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 19:42:59,013 - api_logger - INFO - Response received successfully.
2025-03-24 19:42:59,022 - api_logger - INFO - Inserting record for session_id: new-test1, request_type: interview_experience
2025-03-24 19:42:59,973 - api_logger - INFO - Extracted questions for session_id: new-test1: [{'request_type': 'job_title', 'questions': ['What are the main themes explored in the text?', 'How does the author develop the characters throughout the story?', 'What is the significance of the setting in the narrative?', 'Can you identify any symbols used in the text and their meanings?', 'How does the plot structure contribute to the overall impact of the story?', "What are the motivations behind the protagonist's actions?", 'How does the author use language and style to enhance the story?', 'What conflicts arise in the narrative, and how are they resolved?', 'In what ways does the text reflect the historical or cultural context in which it was written?', 'What are the key takeaways or messages from the text?']}, {'request_type': 'jd', 'questions': ['What are the main themes explored in the text?', 'How does the author develop the characters throughout the story?', 'What is the significance of the setting in the narrative?', 'Can you identify any symbols used in the text and their meanings?', 'How does the plot structure contribute to the overall impact of the story?', "What are the motivations behind the protagonist's actions?", 'How does the author use language and style to enhance the story?', 'What conflicts arise in the narrative, and how are they resolved?', 'In what ways does the text reflect the historical or cultural context in which it was written?', 'What are the key takeaways or messages from the text?']}, {'request_type': 'resume', 'questions': ['What are the main themes explored in the text?', 'How does the author develop the characters throughout the story?', 'What is the significance of the setting in the narrative?', 'Can you identify any symbols used in the text, and what do they represent?', 'How does the plot structure contribute to the overall impact of the story?', "What are the motivations behind the protagonist's actions?", 'How does the author use language and style to convey meaning?', 'What role do secondary characters play in the development of the main character?', 'How does the historical context influence the events of the story?', 'What are the moral or ethical dilemmas presented in the text?']}]
2025-03-24 19:42:59,973 - api_logger - INFO - Interview Experience Request with Session ID: new-test1 Successfully processed
2025-03-24 19:50:04,204 - api_logger - INFO - Request received for Job Title with Session ID: new-test2 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 19:50:06,635 - api_logger - INFO - Job Title Request with Session ID: new-test2 Successfully received and added to background task
2025-03-24 19:50:06,636 - api_logger - INFO - Starting background response generation for session_id: new-test2, job_title: Data Scientist
2025-03-24 19:50:06,636 - api_logger - INFO - Checking if session does not exist for session_id: new-test2, request_type: job_title
2025-03-24 19:50:06,943 - api_logger - INFO - No previous session id
2025-03-24 19:50:06,943 - api_logger - INFO - Inserting status for session_id: new-test2, request_type: job_title, status: Inprogress
2025-03-24 19:50:07,555 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 19:50:09,610 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 19:50:09,610 - api_logger - ERROR - Error occurred: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 19:50:09,613 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 19:50:09,613 - api_logger - ERROR - GPT error for session_id: new-test2 during job_title generation
2025-03-24 19:50:09,614 - api_logger - INFO - Inserting record for session_id: new-test2, request_type: job_title
2025-03-24 19:50:10,217 - api_logger - INFO - Inserted initial status for session_id: new-test2
2025-03-24 19:50:10,217 - api_logger - INFO - Updating status for session_id: new-test2, request_type: job_title, status: complete
2025-03-24 19:50:10,933 - api_logger - INFO - Status updated successfully.
2025-03-24 19:50:10,933 - api_logger - INFO - Response generated for session_id: new-test2, response_time: 2ms
2025-03-24 20:09:29,225 - api_logger - INFO - Request received for Job Title with Session ID: new-test3 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:09:42,185 - api_logger - INFO - Job Title Request with Session ID: new-test3 Successfully received and added to background task
2025-03-24 20:09:42,186 - api_logger - INFO - Starting background response generation for session_id: new-test3, job_title: Data Scientist
2025-03-24 20:09:42,187 - api_logger - INFO - Checking if session does not exist for session_id: new-test3, request_type: job_title
2025-03-24 20:09:42,495 - api_logger - INFO - No previous session id
2025-03-24 20:09:42,498 - api_logger - INFO - Inserting status for session_id: new-test3, request_type: job_title, status: Inprogress
2025-03-24 20:09:43,303 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:09:46,283 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:09:53,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:09:53,064 - api_logger - INFO - Response received successfully.
2025-03-24 20:09:53,064 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test3
2025-03-24 20:09:53,064 - api_logger - ERROR - Exception during background task for session_id: new-test3: not enough values to unpack (expected 2, got 1)
2025-03-24 20:09:53,064 - api_logger - INFO - Updating status for session_id: new-test3, request_type: job_title, status: failed
2025-03-24 20:09:53,854 - api_logger - INFO - Status updated successfully.
2025-03-24 20:12:18,514 - api_logger - INFO - Request received for Job Title with Session ID: new-test3 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:12:20,793 - api_logger - INFO - Job Title Request with Session ID: new-test3 Successfully received and added to background task
2025-03-24 20:12:20,801 - api_logger - INFO - Starting background response generation for session_id: new-test3, job_title: Data Scientist
2025-03-24 20:12:20,801 - api_logger - INFO - Checking if session does not exist for session_id: new-test3, request_type: job_title
2025-03-24 20:12:21,104 - api_logger - INFO - Updating job experience for session_id: new-test3
2025-03-24 20:12:22,124 - api_logger - INFO - Inserting status for session_id: new-test3, request_type: job_title, status: Inprogress
2025-03-24 20:12:22,744 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:12:24,803 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:12:42,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:12:42,846 - api_logger - ERROR - Error occurred: 'Question' object has no attribute 'response_metadata'
2025-03-24 20:12:42,847 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 20:12:42,847 - api_logger - ERROR - GPT error for session_id: new-test3 during job_title generation
2025-03-24 20:12:42,847 - api_logger - INFO - Inserting record for session_id: new-test3, request_type: job_title
2025-03-24 20:12:43,533 - api_logger - INFO - Inserted initial status for session_id: new-test3
2025-03-24 20:12:43,533 - api_logger - INFO - Updating status for session_id: new-test3, request_type: job_title, status: complete
2025-03-24 20:12:44,145 - api_logger - INFO - Status updated successfully.
2025-03-24 20:12:44,146 - api_logger - INFO - Response generated for session_id: new-test3, response_time: 22ms
2025-03-24 20:14:25,874 - api_logger - INFO - Request received for Job Title with Session ID: new-test3 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:14:28,193 - api_logger - INFO - Job Title Request with Session ID: new-test3 Successfully received and added to background task
2025-03-24 20:14:28,203 - api_logger - INFO - Starting background response generation for session_id: new-test3, job_title: Data Scientist
2025-03-24 20:14:28,203 - api_logger - INFO - Checking if session does not exist for session_id: new-test3, request_type: job_title
2025-03-24 20:14:28,484 - api_logger - INFO - Updating job experience for session_id: new-test3
2025-03-24 20:14:29,513 - api_logger - INFO - Inserting status for session_id: new-test3, request_type: job_title, status: Inprogress
2025-03-24 20:14:30,142 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:14:31,972 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:14:49,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:14:49,706 - api_logger - ERROR - Error occurred: 'Question' object has no attribute 'response_metadata'
2025-03-24 20:14:49,707 - api_logger - ERROR - GPT error for session_id: new-test3 during job_title generation
2025-03-24 20:14:49,707 - api_logger - INFO - Inserting record for session_id: new-test3, request_type: job_title
2025-03-24 20:14:50,305 - api_logger - INFO - Inserted initial status for session_id: new-test3
2025-03-24 20:14:50,305 - api_logger - INFO - Updating status for session_id: new-test3, request_type: job_title, status: complete
2025-03-24 20:14:50,924 - api_logger - INFO - Status updated successfully.
2025-03-24 20:14:50,924 - api_logger - INFO - Response generated for session_id: new-test3, response_time: 21ms
2025-03-24 20:15:19,912 - api_logger - INFO - Fetching session data for session ID: new-test3 and is_ca: False
2025-03-24 20:15:22,163 - api_logger - INFO - Fetching session data for session_id: new-test3
2025-03-24 20:15:22,657 - api_logger - INFO - Session data fetched successfully for session ID: new-test3
2025-03-24 20:24:28,877 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:24:31,243 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:24:31,254 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:24:31,254 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:24:31,633 - api_logger - INFO - No previous session id
2025-03-24 20:24:31,634 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:24:32,264 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:24:34,183 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:24:34,285 - api_logger - ERROR - Error occurred: Invalid input type <class 'langchain_core.prompts.chat.ChatPromptTemplate'>. Must be a PromptValue, str, or list of BaseMessages.
2025-03-24 20:24:34,286 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test4
2025-03-24 20:24:34,286 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:24:35,002 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:24:35,003 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:24:35,633 - api_logger - INFO - Status updated successfully.
2025-03-24 20:24:35,633 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 3ms
2025-03-24 20:28:24,294 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:28:26,325 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:28:26,325 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:28:26,325 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:28:26,644 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:28:27,666 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:28:28,284 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:28:30,234 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:28:30,324 - api_logger - ERROR - Error occurred: Completions.create() got an unexpected keyword argument 'return_usage'
2025-03-24 20:28:30,325 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test4
2025-03-24 20:28:30,325 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:28:31,133 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:28:31,133 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:28:31,954 - api_logger - INFO - Status updated successfully.
2025-03-24 20:28:31,954 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 3ms
2025-03-24 20:29:03,593 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:29:05,644 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:29:05,646 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:29:05,646 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:29:05,964 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:29:07,094 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:29:07,693 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:29:09,643 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:29:15,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:29:15,795 - api_logger - ERROR - Error occurred: 'QuestionList' object is not subscriptable
2025-03-24 20:29:15,796 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test4
2025-03-24 20:29:15,797 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:29:16,396 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:29:16,397 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:29:17,009 - api_logger - INFO - Status updated successfully.
2025-03-24 20:29:17,012 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 10ms
2025-03-24 20:30:46,994 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:30:49,068 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:30:49,071 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:30:49,071 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:30:49,398 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:30:50,324 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:30:50,910 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:30:52,765 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:30:59,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:30:59,423 - api_logger - ERROR - Error occurred: 'QuestionList' object has no attribute 'output'
2025-03-24 20:30:59,423 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test4
2025-03-24 20:30:59,423 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:31:00,024 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:31:00,024 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:31:00,647 - api_logger - INFO - Status updated successfully.
2025-03-24 20:31:00,647 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 10ms
2025-03-24 20:31:50,599 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:31:52,886 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:31:52,890 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:31:52,890 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:31:53,192 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:31:54,293 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:31:54,910 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:31:56,751 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:32:04,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:32:04,348 - api_logger - ERROR - Error occurred: 'QuestionList' object is not subscriptable
2025-03-24 20:32:04,348 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test4
2025-03-24 20:32:04,348 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:32:04,948 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:32:04,948 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:32:05,590 - api_logger - INFO - Status updated successfully.
2025-03-24 20:32:05,590 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 11ms
2025-03-24 20:33:15,585 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:33:17,853 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:33:17,853 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:33:17,853 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:33:18,184 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:33:19,386 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:33:20,025 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:33:22,153 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:33:22,153 - api_logger - ERROR - Error occurred: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 20:33:22,153 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 20:33:22,153 - api_logger - ERROR - GPT error for session_id: new-test4 during job_title generation
2025-03-24 20:33:22,153 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:33:22,784 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:33:22,784 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:33:23,403 - api_logger - INFO - Status updated successfully.
2025-03-24 20:33:23,403 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 4ms
2025-03-24 20:34:00,857 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:34:03,114 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:34:03,114 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:34:03,114 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:34:03,524 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:34:04,544 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:34:05,163 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:34:07,214 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:34:07,214 - api_logger - ERROR - Error occurred: BaseChatModel.invoke() missing 1 required positional argument: 'input'
2025-03-24 20:34:07,214 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 20:34:07,214 - api_logger - ERROR - GPT error for session_id: new-test4 during job_title generation
2025-03-24 20:34:07,214 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:34:08,024 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:34:08,024 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:34:08,850 - api_logger - INFO - Status updated successfully.
2025-03-24 20:34:08,850 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 4ms
2025-03-24 20:40:04,173 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:40:06,223 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:40:06,223 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:40:06,223 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:40:06,524 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:40:07,592 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:40:08,283 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:40:10,423 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:40:10,537 - api_logger - ERROR - Error occurred: BaseChatModel.invoke() missing 1 required positional argument: 'input'
2025-03-24 20:40:10,537 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 20:40:10,537 - api_logger - ERROR - GPT error for session_id: new-test4 during job_title generation
2025-03-24 20:40:10,537 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:40:11,237 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:40:11,242 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:40:12,163 - api_logger - INFO - Status updated successfully.
2025-03-24 20:40:12,163 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 4ms
2025-03-24 20:41:02,288 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 20:41:04,894 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 20:41:04,903 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 20:41:04,903 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 20:41:05,323 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 20:41:06,333 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 20:41:07,046 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 20:41:09,213 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 20:41:17,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 20:41:17,752 - api_logger - ERROR - Error occurred: 'AIMessage' object has no attribute 'choices'
2025-03-24 20:41:17,753 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 20:41:17,753 - api_logger - ERROR - GPT error for session_id: new-test4 during job_title generation
2025-03-24 20:41:17,753 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 20:41:18,416 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 20:41:18,416 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 20:41:19,033 - api_logger - INFO - Status updated successfully.
2025-03-24 20:41:19,041 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 12ms
2025-03-24 21:54:16,982 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 21:54:24,295 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 21:54:24,295 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 21:54:24,295 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 21:54:24,612 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 21:54:25,457 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 21:54:26,006 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 21:54:28,094 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 21:54:33,550 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 21:54:33,602 - api_logger - ERROR - Error occurred: 'QuestionList' object has no attribute 'choices'
2025-03-24 21:54:33,602 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 21:54:33,602 - api_logger - ERROR - GPT error for session_id: new-test4 during job_title generation
2025-03-24 21:54:33,602 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 21:54:34,131 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 21:54:34,131 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 21:54:34,695 - api_logger - INFO - Status updated successfully.
2025-03-24 21:54:34,695 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 9ms
2025-03-24 21:57:14,860 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 21:57:16,968 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 21:57:16,973 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 21:57:16,973 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 21:57:17,248 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 21:57:18,121 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 21:57:18,669 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 21:57:20,284 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 21:57:25,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 21:57:25,989 - api_logger - ERROR - Error occurred: cannot access local variable 'gpt4_response' where it is not associated with a value
2025-03-24 21:57:25,989 - api_logger - ERROR - Retry failed: 'ChatOpenAI' object has no attribute 'chat'
2025-03-24 21:57:25,989 - api_logger - ERROR - GPT error for session_id: new-test4 during job_title generation
2025-03-24 21:57:26,002 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 21:57:26,540 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 21:57:26,540 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 21:57:27,107 - api_logger - INFO - Status updated successfully.
2025-03-24 21:57:27,113 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 9ms
2025-03-24 22:03:08,046 - api_logger - INFO - Request received for Job Title with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 22:03:09,742 - api_logger - INFO - Job Title Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 22:03:09,758 - api_logger - INFO - Starting background response generation for session_id: new-test4, job_title: Data Scientist
2025-03-24 22:03:09,759 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: job_title
2025-03-24 22:03:10,009 - api_logger - INFO - Updating job experience for session_id: new-test4
2025-03-24 22:03:10,830 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: job_title, status: Inprogress
2025-03-24 22:03:11,332 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 22:03:12,937 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:03:17,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:03:17,810 - api_logger - INFO - Response received successfully.
2025-03-24 22:03:17,812 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test4
2025-03-24 22:03:17,812 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: job_title
2025-03-24 22:03:18,456 - api_logger - INFO - Inserted initial status for session_id: new-test4
2025-03-24 22:03:18,456 - api_logger - INFO - Updating status for session_id: new-test4, request_type: job_title, status: complete
2025-03-24 22:03:19,044 - api_logger - INFO - Status updated successfully.
2025-03-24 22:03:19,058 - api_logger - INFO - Response generated for session_id: new-test4, response_time: 8ms
2025-03-24 22:10:25,978 - api_logger - INFO - Fetching session data for session ID: new-test4 and is_ca: False
2025-03-24 22:10:27,670 - api_logger - INFO - Fetching session data for session_id: new-test4
2025-03-24 22:10:28,010 - api_logger - INFO - Session data fetched successfully for session ID: new-test4
2025-03-24 22:10:49,473 - api_logger - INFO - Request received for JD with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:10:51,204 - api_logger - INFO - JD Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 22:10:51,212 - api_logger - INFO - Processing JD upload for session_id: new-test4
2025-03-24 22:10:51,212 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: jd
2025-03-24 22:10:51,456 - api_logger - INFO - No previous session id
2025-03-24 22:10:51,456 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: jd, status: Inprogress
2025-03-24 22:10:52,085 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 22:10:53,765 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:10:57,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:10:57,111 - api_logger - INFO - Response received successfully.
2025-03-24 22:10:57,111 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: jd
2025-03-24 22:10:57,621 - api_logger - INFO - JD response generated for session_id: new-test4, response_time: 5.898524522781372s
2025-03-24 22:10:57,621 - api_logger - INFO - Updating status for session_id: new-test4, request_type: jd, status: complete
2025-03-24 22:10:58,121 - api_logger - INFO - Status updated successfully.
2025-03-24 22:11:35,763 - api_logger - INFO - Request received for Resume with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:11:37,729 - api_logger - INFO - Resume Request with Session ID: new-test4 Successfully received and added to background task
2025-03-24 22:11:37,734 - api_logger - INFO - Processing resume for session_id: new-test4
2025-03-24 22:11:37,734 - api_logger - INFO - Checking if session does not exist for session_id: new-test4, request_type: resume
2025-03-24 22:11:37,991 - api_logger - INFO - No previous session id found for session_id: new-test4
2025-03-24 22:11:37,991 - api_logger - INFO - Inserting status for session_id: new-test4, request_type: resume, status: Inprogress
2025-03-24 22:11:38,514 - api_logger - INFO - Getting file text for: data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 22:11:38,541 - api_logger - INFO - Processing PDF file.
2025-03-24 22:11:38,719 - api_logger - INFO - File text extraction completed.
2025-03-24 22:11:38,720 - api_logger - INFO - Successfully extracted resume text for session_id: new-test4
2025-03-24 22:11:38,720 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 22:11:40,409 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:11:48,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:11:48,735 - api_logger - INFO - Response received successfully.
2025-03-24 22:11:48,735 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test4
2025-03-24 22:11:48,735 - api_logger - INFO - Inserting record for session_id: new-test4, request_type: resume
2025-03-24 22:11:49,304 - api_logger - INFO - Updating status for session_id: new-test4, request_type: resume, status: complete
2025-03-24 22:11:49,861 - api_logger - INFO - Status updated successfully.
2025-03-24 22:11:49,861 - api_logger - INFO - Resume response generated for session_id: new-test4, response_time: 11ms
2025-03-24 22:11:58,252 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:12:00,171 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:12:00,172 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test4
2025-03-24 22:12:00,423 - api_logger - INFO - Job status for session new-test4: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:12:00,423 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test4
2025-03-24 22:12:00,423 - api_logger - INFO - Processing interview for session_id: new-test4
2025-03-24 22:12:00,423 - api_logger - INFO - Fetching requests for session_id: new-test4
2025-03-24 22:12:00,714 - api_logger - INFO - Extracting questions from responses for session_id: new-test4
2025-03-24 22:12:00,714 - api_logger - ERROR - Error parsing GPT response for session_id: new-test4 and request_type: job_title
2025-03-24 22:12:00,714 - api_logger - ERROR - Skipping this response for session_id: new-test4 and request_type: job_title
2025-03-24 22:12:00,714 - api_logger - ERROR - Error parsing GPT response for session_id: new-test4 and request_type: jd
2025-03-24 22:12:00,717 - api_logger - ERROR - Skipping this response for session_id: new-test4 and request_type: jd
2025-03-24 22:12:00,717 - api_logger - ERROR - Error parsing GPT response for session_id: new-test4 and request_type: resume
2025-03-24 22:12:00,717 - api_logger - ERROR - Skipping this response for session_id: new-test4 and request_type: resume
2025-03-24 22:12:00,717 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 22:12:00,717 - api_logger - INFO - Fetching role and experience level for session_id: new-test4
2025-03-24 22:12:01,133 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test4
2025-03-24 22:12:01,137 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:12:01,137 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:12:02,824 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:12:02,832 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:12:05,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:12:05,358 - api_logger - INFO - Response received successfully.
2025-03-24 22:12:05,358 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test4
2025-03-24 22:13:04,610 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:13:06,211 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:13:06,211 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test4
2025-03-24 22:13:06,624 - api_logger - INFO - Job status for session new-test4: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:13:06,624 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test4
2025-03-24 22:13:06,624 - api_logger - INFO - Processing interview for session_id: new-test4
2025-03-24 22:13:06,624 - api_logger - INFO - Fetching requests for session_id: new-test4
2025-03-24 22:13:07,009 - api_logger - INFO - Extracting questions from responses for session_id: new-test4
2025-03-24 22:13:07,013 - api_logger - ERROR - Error parsing GPT response for session_id: new-test4 and request_type: job_title
2025-03-24 22:13:07,013 - api_logger - ERROR - Skipping this response for session_id: new-test4 and request_type: job_title
2025-03-24 22:13:07,013 - api_logger - ERROR - Error parsing GPT response for session_id: new-test4 and request_type: jd
2025-03-24 22:13:07,013 - api_logger - ERROR - Skipping this response for session_id: new-test4 and request_type: jd
2025-03-24 22:13:07,018 - api_logger - ERROR - Error parsing GPT response for session_id: new-test4 and request_type: resume
2025-03-24 22:13:07,018 - api_logger - ERROR - Skipping this response for session_id: new-test4 and request_type: resume
2025-03-24 22:13:07,019 - api_logger - INFO - Extracted 0 questions from responses.
2025-03-24 22:13:07,019 - api_logger - INFO - Fetching role and experience level for session_id: new-test4
2025-03-24 22:13:07,313 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test4
2025-03-24 22:13:07,313 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:13:07,313 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:13:08,841 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:13:08,841 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:13:12,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:13:12,707 - api_logger - INFO - Response received successfully.
2025-03-24 22:13:12,708 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test4
2025-03-24 22:16:40,314 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:16:42,008 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:16:42,012 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test4
2025-03-24 22:16:42,276 - api_logger - INFO - Job status for session new-test4: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:16:42,277 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test4
2025-03-24 22:16:42,277 - api_logger - INFO - Processing interview for session_id: new-test4
2025-03-24 22:16:42,277 - api_logger - INFO - Fetching requests for session_id: new-test4
2025-03-24 22:16:42,637 - api_logger - INFO - Extracting questions from responses for session_id: new-test4
2025-03-24 22:16:42,642 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:16:42,642 - api_logger - INFO - Fetching role and experience level for session_id: new-test4
2025-03-24 22:16:42,973 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test4
2025-03-24 22:16:42,973 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:16:42,974 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:16:44,604 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:16:44,604 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:16:48,603 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:16:48,634 - api_logger - INFO - Response received successfully.
2025-03-24 22:16:48,634 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test4
2025-03-24 22:18:14,678 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:18:16,752 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test4 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:18:16,752 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test4
2025-03-24 22:18:17,120 - api_logger - INFO - Job status for session new-test4: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:18:17,130 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test4
2025-03-24 22:18:17,130 - api_logger - INFO - Processing interview for session_id: new-test4
2025-03-24 22:18:17,130 - api_logger - INFO - Fetching requests for session_id: new-test4
2025-03-24 22:18:17,444 - api_logger - INFO - Extracting questions from responses for session_id: new-test4
2025-03-24 22:18:17,449 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:18:17,450 - api_logger - INFO - Fetching role and experience level for session_id: new-test4
2025-03-24 22:18:17,860 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test4
2025-03-24 22:18:17,860 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:18:17,860 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:18:19,640 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:18:19,640 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:18:22,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:18:23,001 - api_logger - INFO - Response received successfully.
2025-03-24 22:18:23,001 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not QuestionList Occurred while processing interview experience request for session ID: new-test4
2025-03-24 22:22:57,364 - api_logger - INFO - Request received for Job Title with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 22:22:59,524 - api_logger - INFO - Job Title Request with Session ID: new-test5 Successfully received and added to background task
2025-03-24 22:22:59,527 - api_logger - INFO - Starting background response generation for session_id: new-test5, job_title: Data Scientist
2025-03-24 22:22:59,530 - api_logger - INFO - Checking if session does not exist for session_id: new-test5, request_type: job_title
2025-03-24 22:22:59,832 - api_logger - INFO - No previous session id
2025-03-24 22:22:59,832 - api_logger - INFO - Inserting status for session_id: new-test5, request_type: job_title, status: Inprogress
2025-03-24 22:23:00,412 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 22:23:02,097 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:23:08,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:23:08,162 - api_logger - INFO - Response received successfully.
2025-03-24 22:23:08,162 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test5
2025-03-24 22:23:08,164 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: job_title
2025-03-24 22:23:08,164 - api_logger - ERROR - An error occurred while inserting record: dict can not be used as parameter
2025-03-24 22:23:08,164 - api_logger - INFO - Inserted initial status for session_id: new-test5
2025-03-24 22:23:08,164 - api_logger - INFO - Updating status for session_id: new-test5, request_type: job_title, status: complete
2025-03-24 22:23:08,704 - api_logger - INFO - Status updated successfully.
2025-03-24 22:23:08,704 - api_logger - INFO - Response generated for session_id: new-test5, response_time: 8ms
2025-03-24 22:23:15,127 - api_logger - INFO - Request received for JD with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:23:16,888 - api_logger - INFO - JD Request with Session ID: new-test5 Successfully received and added to background task
2025-03-24 22:23:16,893 - api_logger - INFO - Processing JD upload for session_id: new-test5
2025-03-24 22:23:16,893 - api_logger - INFO - Checking if session does not exist for session_id: new-test5, request_type: jd
2025-03-24 22:23:17,163 - api_logger - INFO - No previous session id
2025-03-24 22:23:17,164 - api_logger - INFO - Inserting status for session_id: new-test5, request_type: jd, status: Inprogress
2025-03-24 22:23:17,767 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 22:23:19,434 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:23:23,525 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:23:23,533 - api_logger - INFO - Response received successfully.
2025-03-24 22:23:23,537 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: jd
2025-03-24 22:23:23,537 - api_logger - ERROR - An error occurred while inserting record: dict can not be used as parameter
2025-03-24 22:23:23,537 - api_logger - INFO - JD response generated for session_id: new-test5, response_time: 6.640223026275635s
2025-03-24 22:23:23,538 - api_logger - INFO - Updating status for session_id: new-test5, request_type: jd, status: complete
2025-03-24 22:23:24,074 - api_logger - INFO - Status updated successfully.
2025-03-24 22:23:30,379 - api_logger - INFO - Request received for Resume with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:23:32,044 - api_logger - INFO - Resume Request with Session ID: new-test5 Successfully received and added to background task
2025-03-24 22:23:32,046 - api_logger - INFO - Processing resume for session_id: new-test5
2025-03-24 22:23:32,046 - api_logger - INFO - Checking if session does not exist for session_id: new-test5, request_type: resume
2025-03-24 22:23:32,309 - api_logger - INFO - No previous session id found for session_id: new-test5
2025-03-24 22:23:32,313 - api_logger - INFO - Inserting status for session_id: new-test5, request_type: resume, status: Inprogress
2025-03-24 22:23:32,874 - api_logger - INFO - Getting file text for: data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 22:23:32,895 - api_logger - INFO - Processing PDF file.
2025-03-24 22:23:32,976 - api_logger - INFO - File text extraction completed.
2025-03-24 22:23:32,976 - api_logger - INFO - Successfully extracted resume text for session_id: new-test5
2025-03-24 22:23:32,976 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 22:23:36,108 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:23:42,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:23:42,477 - api_logger - INFO - Response received successfully.
2025-03-24 22:23:42,477 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test5
2025-03-24 22:23:42,477 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: resume
2025-03-24 22:23:42,482 - api_logger - ERROR - An error occurred while inserting record: dict can not be used as parameter
2025-03-24 22:23:42,482 - api_logger - INFO - Updating status for session_id: new-test5, request_type: resume, status: complete
2025-03-24 22:23:43,006 - api_logger - INFO - Status updated successfully.
2025-03-24 22:23:43,006 - api_logger - INFO - Resume response generated for session_id: new-test5, response_time: 10ms
2025-03-24 22:24:20,830 - api_logger - INFO - Fetching session data for session ID: new-test5 and is_ca: False
2025-03-24 22:24:22,558 - api_logger - INFO - Fetching session data for session_id: new-test5
2025-03-24 22:24:22,908 - api_logger - ERROR - Exception: 404: Session not found Occurred while fetching session data
2025-03-24 22:24:22,908 - api_logger - INFO - Session data fetched successfully for session ID: new-test5
2025-03-24 22:26:48,770 - api_logger - INFO - Request received for Job Title with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 22:26:50,508 - api_logger - INFO - Job Title Request with Session ID: new-test5 Successfully received and added to background task
2025-03-24 22:26:50,509 - api_logger - INFO - Starting background response generation for session_id: new-test5, job_title: Data Scientist
2025-03-24 22:26:50,509 - api_logger - INFO - Checking if session does not exist for session_id: new-test5, request_type: job_title
2025-03-24 22:26:50,769 - api_logger - INFO - Updating job experience for session_id: new-test5
2025-03-24 22:26:51,650 - api_logger - INFO - Inserting status for session_id: new-test5, request_type: job_title, status: Inprogress
2025-03-24 22:26:52,220 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 22:26:53,981 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:26:58,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:26:58,137 - api_logger - INFO - Response received successfully.
2025-03-24 22:26:58,137 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test5
2025-03-24 22:26:58,139 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: job_title
2025-03-24 22:26:58,685 - api_logger - INFO - Inserted initial status for session_id: new-test5
2025-03-24 22:26:58,685 - api_logger - INFO - Updating status for session_id: new-test5, request_type: job_title, status: complete
2025-03-24 22:26:59,252 - api_logger - INFO - Status updated successfully.
2025-03-24 22:26:59,252 - api_logger - INFO - Response generated for session_id: new-test5, response_time: 7ms
2025-03-24 22:27:06,711 - api_logger - INFO - Request received for JD with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:27:08,410 - api_logger - INFO - JD Request with Session ID: new-test5 Successfully received and added to background task
2025-03-24 22:27:08,422 - api_logger - INFO - Processing JD upload for session_id: new-test5
2025-03-24 22:27:08,422 - api_logger - INFO - Checking if session does not exist for session_id: new-test5, request_type: jd
2025-03-24 22:27:08,681 - api_logger - INFO - No previous session id
2025-03-24 22:27:08,681 - api_logger - INFO - Inserting status for session_id: new-test5, request_type: jd, status: Inprogress
2025-03-24 22:27:09,207 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 22:27:10,841 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:27:14,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:27:14,220 - api_logger - INFO - Response received successfully.
2025-03-24 22:27:14,221 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: jd
2025-03-24 22:27:14,863 - api_logger - INFO - JD response generated for session_id: new-test5, response_time: 5.7987329959869385s
2025-03-24 22:27:14,863 - api_logger - INFO - Updating status for session_id: new-test5, request_type: jd, status: complete
2025-03-24 22:27:15,371 - api_logger - INFO - Status updated successfully.
2025-03-24 22:27:20,875 - api_logger - INFO - Request received for Resume with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:27:22,742 - api_logger - INFO - Resume Request with Session ID: new-test5 Successfully received and added to background task
2025-03-24 22:27:22,744 - api_logger - INFO - Processing resume for session_id: new-test5
2025-03-24 22:27:22,747 - api_logger - INFO - Checking if session does not exist for session_id: new-test5, request_type: resume
2025-03-24 22:27:23,031 - api_logger - INFO - No previous session id found for session_id: new-test5
2025-03-24 22:27:23,032 - api_logger - INFO - Inserting status for session_id: new-test5, request_type: resume, status: Inprogress
2025-03-24 22:27:23,543 - api_logger - INFO - Getting file text for: data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 22:27:23,574 - api_logger - INFO - Processing PDF file.
2025-03-24 22:27:23,726 - api_logger - INFO - File text extraction completed.
2025-03-24 22:27:23,726 - api_logger - INFO - Successfully extracted resume text for session_id: new-test5
2025-03-24 22:27:23,726 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 22:27:25,683 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:27:30,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:27:30,482 - api_logger - INFO - Response received successfully.
2025-03-24 22:27:30,482 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test5
2025-03-24 22:27:30,484 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: resume
2025-03-24 22:27:31,023 - api_logger - INFO - Updating status for session_id: new-test5, request_type: resume, status: complete
2025-03-24 22:27:31,562 - api_logger - INFO - Status updated successfully.
2025-03-24 22:27:31,562 - api_logger - INFO - Resume response generated for session_id: new-test5, response_time: 7ms
2025-03-24 22:28:13,775 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:28:15,696 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:28:15,696 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:28:16,007 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:28:16,007 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:28:16,007 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:28:16,007 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:28:16,315 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:28:16,321 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:28:16,321 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:28:16,596 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:28:16,602 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:28:16,603 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:28:16,603 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:28:18,529 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:28:18,529 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:28:21,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:28:21,130 - api_logger - INFO - Response received successfully.
2025-03-24 22:28:21,130 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not dict Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:30:34,343 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:30:36,205 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:30:36,206 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:30:36,610 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:30:36,610 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:30:36,610 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:30:36,610 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:30:37,020 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:30:37,023 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:30:37,024 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:30:37,320 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:30:37,325 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:30:37,325 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:30:37,325 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:30:39,118 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:30:39,118 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:30:41,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:30:41,906 - api_logger - INFO - Response received successfully.
2025-03-24 22:30:41,906 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not dict Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:32:08,516 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:32:10,112 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:32:10,112 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:32:10,394 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:32:10,402 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:32:10,402 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:32:10,402 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:32:10,692 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:32:10,694 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:32:10,694 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:32:10,980 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:32:10,980 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:32:10,980 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:32:10,980 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:32:12,416 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:32:12,416 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:32:14,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:32:14,634 - api_logger - INFO - Response received successfully.
2025-03-24 22:32:14,641 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not dict Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:32:51,386 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:32:53,128 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:32:53,128 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:32:53,358 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:32:53,358 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:32:53,358 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:32:53,358 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:32:53,657 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:32:53,657 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:32:53,665 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:32:53,938 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:32:53,938 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:32:53,940 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:32:53,940 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:32:55,425 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:32:55,426 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:32:58,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:32:58,816 - api_logger - INFO - Response received successfully.
2025-03-24 22:32:58,816 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not dict Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:35:14,665 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:35:16,586 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:35:16,594 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:35:16,891 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:35:16,891 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:35:16,893 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:35:16,894 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:35:17,205 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:35:17,217 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:35:17,217 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:35:17,530 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:35:17,532 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:35:17,533 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:35:17,534 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:35:19,077 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:35:19,077 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:35:23,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:35:23,787 - api_logger - INFO - Response received successfully.
2025-03-24 22:35:23,787 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not dict Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:35:33,999 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:35:36,197 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:35:36,197 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:35:36,660 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:35:36,665 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:35:36,665 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:35:36,665 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:35:37,027 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:35:37,027 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:35:37,027 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:35:37,340 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:35:37,340 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:35:37,340 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:35:37,345 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:35:39,079 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:35:39,079 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:35:45,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:35:45,791 - api_logger - INFO - Response received successfully.
2025-03-24 22:35:45,792 - api_logger - ERROR - Exception: the JSON object must be str, bytes or bytearray, not dict Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:39:13,761 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:39:15,558 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:39:15,558 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:39:15,842 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:39:15,842 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:39:15,842 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:39:15,842 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:39:16,150 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:39:16,158 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:39:16,161 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:39:16,467 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:39:16,468 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:39:16,468 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:39:16,468 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:39:18,109 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:39:18,113 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:39:21,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:39:21,420 - api_logger - INFO - Response received successfully.
2025-03-24 22:39:21,423 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: interview_experience
2025-03-24 22:39:21,998 - api_logger - INFO - Extracted questions for session_id: new-test5: [{'request_type': 'job_title', 'questions': ['What programming languages are you proficient in for data analysis and modeling?', 'Can you describe your experience with statistical analysis and when you have applied these skills in a real-world scenario?', 'How familiar are you with machine learning algorithms, and can you provide an example of a project where you implemented one?', 'Which libraries or frameworks do you prefer for data manipulation and visualization, and why?', 'How do you handle missing or incomplete data in your datasets?', 'Can you explain your approach to feature selection and engineering in a dataset?', 'What methods do you use to evaluate the performance of your models?', 'How do you ensure your data analysis is reproducible and maintainable?', 'Can you discuss a challenging dataset you worked with and how you overcame any issues?', 'What tools and technologies do you use for data storage and retrieval?', 'Describe your experience with A/B testing and how you analyze the results.', 'How do you keep up with the advancements in the field of data science?', 'What are your thoughts on the ethical considerations in data science?', 'How has your previous experience prepared you for this role?', 'Could you describe a situation where you had to prioritize competing tasks related to data science, and how did you manage your time and resources effectively?', 'How do you approach training or mentoring team members who may not be as proficient in data science skills?']}, {'request_type': 'jd', 'questions': ['Why do you want to work here?', 'Do you have any experience with [specific aspect]?', 'What is your experience with [specific requirement]?', 'This role involves [specific responsibility]. How would you [action related to that responsibility]?', 'At our company, we use [specific software tool]. How much experience do you have with [that tool], and in what context have you used it before?', 'What experience do you have using [specific software tool], such as [tool name]?', 'In this position, you will be [specific task/responsibility]. Do you have any experience [performing that task/responsibility]? Can you tell me about it?', 'How would you explain what [specific concept] is to someone with no prior experience?', 'Can you share an instance where you encountered a significant challenge at work and describe the steps you took to address it?', 'Have you ever taken the lead on a project or initiative? How did you motivate and guide your team to achieve success?']}, {'request_type': 'resume', 'questions': ['What was your favorite course at Indian Institute of Information Technology (IIIT) Vadodara?', 'Your resume mentioned that you have experience in Python. How have you utilized this skill in your previous roles?', 'Can you tell me more about your time at FutureSmart AI? Describe your role and responsibilities there.', 'Tell me more about how you developed the Natural Language to SQL system at FutureSmart AI.', 'What are some of the lessons you learned while at Indian Institute of Information Technology (IIIT) Vadodara that would be relevant to the roles you are applying for?', 'How has your previous experience at FutureSmart AI prepared you for this role?', 'How will your expertise in NLP contribute to your success in this role?', 'How did you handle the development of the resume shortlisting system while at FutureSmart AI?', 'Your resume mentioned that you led the integration of sentiment analysis for intent detection. Could you provide more details on how you accomplished this?', 'I noticed that you were selected as one of the top 650 leads across India for the Google Developer Student Club. What is one valuable lesson you learned while holding that position?', 'Describe a scenario where you had to adapt to changes in project scope or timeline. How did you manage the adjustments, and what was the outcome?', 'Tell me about a time when you faced a setback or challenge at work. How did you handle it, and what did you learn from that experience?']}]
2025-03-24 22:39:22,002 - api_logger - INFO - Interview Experience Request with Session ID: new-test5 Successfully processed
2025-03-24 22:50:51,364 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:50:51,426 - api_logger - ERROR - Exception: (2003, "Can't connect to MySQL server on 'ai-prod.c87oewlzrnhl.us-east-1.rds.amazonaws.com' ([Errno 11001] getaddrinfo failed)") Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:51:09,582 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:51:09,584 - api_logger - ERROR - Exception: (2003, "Can't connect to MySQL server on 'ai-prod.c87oewlzrnhl.us-east-1.rds.amazonaws.com' ([Errno 11001] getaddrinfo failed)") Occurred while processing interview experience request for session ID: new-test5
2025-03-24 22:52:12,014 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:52:14,637 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test5 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:52:14,638 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test5
2025-03-24 22:52:14,935 - api_logger - INFO - Job status for session new-test5: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:52:14,936 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test5
2025-03-24 22:52:14,937 - api_logger - INFO - Processing interview for session_id: new-test5
2025-03-24 22:52:14,937 - api_logger - INFO - Fetching requests for session_id: new-test5
2025-03-24 22:52:15,258 - api_logger - INFO - Extracting questions from responses for session_id: new-test5
2025-03-24 22:52:15,260 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:52:15,265 - api_logger - INFO - Fetching role and experience level for session_id: new-test5
2025-03-24 22:52:15,608 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:52:15,608 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test5
2025-03-24 22:52:15,608 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:52:15,608 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:52:17,865 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:52:17,865 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:52:22,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:52:22,657 - api_logger - INFO - Response received successfully.
2025-03-24 22:52:22,657 - api_logger - INFO - Inserting record for session_id: new-test5, request_type: interview_experience
2025-03-24 22:52:23,217 - api_logger - INFO - Extracted questions for session_id: new-test5: [{'request_type': 'job_title', 'questions': ['What programming languages are you proficient in for data analysis and modeling?', 'Can you describe your experience with statistical analysis and when you have applied these skills in a real-world scenario?', 'How familiar are you with machine learning algorithms, and can you provide an example of a project where you implemented one?', 'Which libraries or frameworks do you prefer for data manipulation and visualization, and why?', 'How do you handle missing or incomplete data in your datasets?', 'Can you explain your approach to feature selection and engineering in a dataset?', 'What methods do you use to evaluate the performance of your models?', 'How do you ensure your data analysis is reproducible and maintainable?', 'Can you discuss a challenging dataset you worked with and how you overcame any issues?', 'What tools and technologies do you use for data storage and retrieval?', 'Describe your experience with A/B testing and how you analyze the results.', 'How do you keep up with the advancements in the field of data science?', 'What are your thoughts on the ethical considerations in data science?', 'How has your previous experience prepared you for this role?', 'Could you describe a situation where you had to prioritize competing tasks related to data science, and how did you manage your time and resources effectively?', 'How do you approach training or mentoring team members who may not be as proficient in data science skills?']}, {'request_type': 'jd', 'questions': ['Why do you want to work here?', 'Do you have any experience with [specific aspect]?', 'What is your experience with [specific requirement]?', 'This role involves [specific responsibility]. How would you [action related to that responsibility]?', 'At our company, we use [specific software tool]. How much experience do you have with [that tool], and in what context have you used it before?', 'What experience do you have using [specific software tool], such as [tool name]?', 'In this position, you will be [specific task/responsibility]. Do you have any experience [performing that task/responsibility]? Can you tell me about it?', 'How would you explain what [specific concept] is to someone with no prior experience?', 'Can you share an instance where you encountered a significant challenge at work and describe the steps you took to address it?', 'Have you ever taken the lead on a project or initiative? How did you motivate and guide your team to achieve success?']}, {'request_type': 'resume', 'questions': ['What was your favorite course at Indian Institute of Information Technology (IIIT) Vadodara?', 'Your resume mentioned that you have experience in Python. How have you utilized this skill in your previous roles?', 'Can you tell me more about your time at FutureSmart AI? Describe your role and responsibilities there.', 'Tell me more about how you developed the Natural Language to SQL system at FutureSmart AI.', 'What are some of the lessons you learned while at Indian Institute of Information Technology (IIIT) Vadodara that would be relevant to the roles you are applying for?', 'How has your previous experience at FutureSmart AI prepared you for this role?', 'How will your expertise in NLP contribute to your success in this role?', 'How did you handle the development of the resume shortlisting system while at FutureSmart AI?', 'Your resume mentioned that you led the integration of sentiment analysis for intent detection. Could you provide more details on how you accomplished this?', 'I noticed that you were selected as one of the top 650 leads across India for the Google Developer Student Club. What is one valuable lesson you learned while holding that position?', 'Describe a scenario where you had to adapt to changes in project scope or timeline. How did you manage the adjustments, and what was the outcome?', 'Tell me about a time when you faced a setback or challenge at work. How did you handle it, and what did you learn from that experience?']}]
2025-03-24 22:52:23,217 - api_logger - INFO - Interview Experience Request with Session ID: new-test5 Successfully processed
2025-03-24 22:54:15,461 - api_logger - INFO - Request received for Job Title with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False and Experience Level: None and Number of Questions: 7
2025-03-24 22:54:17,158 - api_logger - INFO - Job Title Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 22:54:17,160 - api_logger - INFO - Starting background response generation for session_id: new-test6, job_title: data Scientist
2025-03-24 22:54:17,161 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: job_title
2025-03-24 22:54:17,414 - api_logger - INFO - No previous session id
2025-03-24 22:54:17,414 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: job_title, status: Inprogress
2025-03-24 22:54:17,941 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-03-24 22:54:19,558 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:54:26,682 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:54:26,725 - api_logger - INFO - Response received successfully.
2025-03-24 22:54:26,725 - api_logger - INFO - Succesfully generated job title questions for session_id: new-test6
2025-03-24 22:54:26,725 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: job_title
2025-03-24 22:54:27,285 - api_logger - INFO - Inserted initial status for session_id: new-test6
2025-03-24 22:54:27,285 - api_logger - INFO - Updating status for session_id: new-test6, request_type: job_title, status: complete
2025-03-24 22:54:27,799 - api_logger - INFO - Status updated successfully.
2025-03-24 22:54:27,799 - api_logger - INFO - Response generated for session_id: new-test6, response_time: 9ms
2025-03-24 22:55:25,815 - api_logger - INFO - Request received for JD with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:55:27,588 - api_logger - INFO - JD Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 22:55:27,588 - api_logger - INFO - Processing JD upload for session_id: new-test6
2025-03-24 22:55:27,588 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: jd
2025-03-24 22:55:27,826 - api_logger - INFO - No previous session id
2025-03-24 22:55:27,826 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: jd, status: Inprogress
2025-03-24 22:55:28,423 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 22:55:30,185 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:55:35,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:55:35,311 - api_logger - INFO - Response received successfully.
2025-03-24 22:55:35,311 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: jd
2025-03-24 22:55:35,808 - api_logger - INFO - JD response generated for session_id: new-test6, response_time: 7.722990036010742s
2025-03-24 22:55:35,808 - api_logger - INFO - Updating status for session_id: new-test6, request_type: jd, status: complete
2025-03-24 22:55:36,308 - api_logger - INFO - Status updated successfully.
2025-03-24 22:56:16,225 - api_logger - INFO - Request received for JD with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:56:17,816 - api_logger - INFO - Getting file text for: data\uploaded_ds_jd.pdf
2025-03-24 22:56:17,825 - api_logger - INFO - Processing PDF file.
2025-03-24 22:56:17,988 - api_logger - INFO - File text extraction completed.
2025-03-24 22:56:17,989 - api_logger - INFO - JD Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 22:56:17,989 - api_logger - INFO - Processing JD upload for session_id: new-test6
2025-03-24 22:56:17,989 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: jd
2025-03-24 22:56:18,237 - api_logger - INFO - Updating job description for session_id: new-test6
2025-03-24 22:56:19,313 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: jd, status: Inprogress
2025-03-24 22:56:19,820 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 22:56:21,403 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:56:25,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:56:25,769 - api_logger - INFO - Response received successfully.
2025-03-24 22:56:25,769 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: jd
2025-03-24 22:56:26,282 - api_logger - INFO - JD response generated for session_id: new-test6, response_time: 7.780106782913208s
2025-03-24 22:56:26,282 - api_logger - INFO - Updating status for session_id: new-test6, request_type: jd, status: complete
2025-03-24 22:56:26,798 - api_logger - INFO - Status updated successfully.
2025-03-24 22:57:09,629 - api_logger - INFO - Request received for Resume with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:57:11,407 - api_logger - INFO - Resume Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 22:57:11,407 - api_logger - INFO - Processing resume for session_id: new-test6
2025-03-24 22:57:11,407 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: resume
2025-03-24 22:57:11,660 - api_logger - INFO - No previous session id found for session_id: new-test6
2025-03-24 22:57:11,660 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: resume, status: Inprogress
2025-03-24 22:57:12,176 - api_logger - INFO - Getting file text for: data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 22:57:12,176 - api_logger - INFO - Processing PDF file.
2025-03-24 22:57:12,285 - api_logger - INFO - File text extraction completed.
2025-03-24 22:57:12,285 - api_logger - INFO - Successfully extracted resume text for session_id: new-test6
2025-03-24 22:57:12,285 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 22:57:14,571 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:57:20,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:57:21,002 - api_logger - INFO - Response received successfully.
2025-03-24 22:57:21,002 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test6
2025-03-24 22:57:21,003 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: resume
2025-03-24 22:57:21,515 - api_logger - INFO - Updating status for session_id: new-test6, request_type: resume, status: complete
2025-03-24 22:57:22,017 - api_logger - INFO - Status updated successfully.
2025-03-24 22:57:22,017 - api_logger - INFO - Resume response generated for session_id: new-test6, response_time: 9ms
2025-03-24 22:57:44,222 - api_logger - INFO - Request received for Interview Experience with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:57:45,857 - api_logger - INFO - Processing request for Interview Experience with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 22:57:45,857 - api_logger - INFO - Checking if all tasks are complete for session_id: new-test6
2025-03-24 22:57:46,140 - api_logger - INFO - Job status for session new-test6: [{'status': 'complete', 'request_type': 'jd'}, {'status': 'complete', 'request_type': 'job_title'}, {'status': 'complete', 'request_type': 'resume'}]
2025-03-24 22:57:46,140 - api_logger - INFO - Proceeding with tasks completion status for session ID: new-test6
2025-03-24 22:57:46,140 - api_logger - INFO - Processing interview for session_id: new-test6
2025-03-24 22:57:46,140 - api_logger - INFO - Fetching requests for session_id: new-test6
2025-03-24 22:57:46,441 - api_logger - INFO - Extracting questions from responses for session_id: new-test6
2025-03-24 22:57:46,441 - api_logger - INFO - Extracted 3 questions from responses.
2025-03-24 22:57:46,441 - api_logger - INFO - Fetching role and experience level for session_id: new-test6
2025-03-24 22:57:46,757 - api_logger - INFO - SQL result: [{'role': 'data Scientist', 'experience_level': None, 'number_of_questions': 7}]
2025-03-24 22:57:46,757 - api_logger - INFO - Role: data Scientist, Experience Level: None, Number of Questions: 7 for session_id: new-test6
2025-03-24 22:57:46,757 - api_logger - INFO - Creating interview experience messages.
2025-03-24 22:57:46,757 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-03-24 22:57:48,386 - api_logger - INFO - Interview experience messages created successfully.
2025-03-24 22:57:48,386 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 22:57:51,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 22:57:51,672 - api_logger - INFO - Response received successfully.
2025-03-24 22:57:51,672 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: interview_experience
2025-03-24 22:57:52,223 - api_logger - INFO - Extracted questions for session_id: new-test6: [{'request_type': 'job_title', 'questions': ['What programming languages are you proficient in, and can you provide examples of projects where you utilized these languages?', 'Can you explain your experience with statistical analysis and the tools you typically use for this process?', 'Describe your proficiency with data manipulation and analysis libraries, such as Pandas or NumPy. Can you give an example of a project where you used these libraries?', 'How do you approach data visualization? What tools do you prefer, and can you share an experience of a visualization that effectively communicated your findings?', 'Could you detail your experience with machine learning algorithms? Which algorithms have you implemented, and what were the outcomes?', 'What is your familiarity with big data technologies, such as Hadoop or Spark? Can you describe a situation where you applied one of these technologies?', 'How do you ensure the accuracy and integrity of your data before conducting analysis?', 'Can you discuss a time when you had to interpret complex data sets and present your findings to an audience? What was your approach?', 'In your experience, what is the most challenging aspect of data cleaning, and how do you typically overcome these challenges?', 'What statistical methods do you find most useful for drawing conclusions from data, and why?', 'How has your previous experience prepared you for this role in terms of handling large data sets and deriving actionable insights?', "How do you stay current with new tools and technologies in data science, and can you provide an example of how you've applied a recent technology in your work?", 'Describe a situation where you had to prioritize competing tasks related to data analysis. How did you manage your time and resources effectively?', 'How do you approach training or mentoring team members who may not be as proficient in data science skills?', 'Can you provide an example of a successful project you completed from start to finish? What were the key factors that contributed to its success?', 'What are some ethical considerations you take into account when working with data?', 'How do you handle ambiguous requirements in a data science project? Can you give an example?', 'What experience do you have with A/B testing or experimental design, and how have you applied that knowledge in your work?']}, {'request_type': 'jd', 'questions': ['Why do you want to work here?', 'Do you have any experience with data mining?', 'What is your experience with machine learning?', 'This role involves building algorithms and machine learning models. How would you approach developing a predictive model?', 'At our company, we use Python for data analysis. How much experience do you have with Python, and in what context have you used it before?', 'In this position, you will be preparing data presentations after necessary data processing. Do you have any experience preparing presentations? Can you tell me about it?', 'How would you explain what machine learning is to someone with no prior experience?', 'Can you share an instance where you encountered a significant challenge at work and describe the steps you took to address it?', 'Have you ever taken the lead on a project or initiative? How did you motivate and guide your team to achieve success?']}, {'request_type': 'resume', 'questions': ['What was your favorite course at Indian Institute of Information Technology (IIIT) Vadodara?', 'Your resume mentioned that you have experience in Natural Language Processing (NLP). How have you utilized this skill in your previous roles?', 'Can you tell me more about your time at FutureSmart AI? Describe your role and responsibilities there.', 'Tell me more about how you developed and integrated NLP applications during your internship at FutureSmart AI.', 'How has your previous experience at FutureSmart AI prepared you for this role?', 'How will your expertise in Python contribute to your success in this role?', 'How did you handle enhancing data analytics capabilities for high-profile clients while you were at FutureSmart AI?', 'On your resume, you mentioned that you developed the AutoQueryNet tool. Could you describe your typical approach to utilizing your skills in developing such a tool?', 'Your resume mentioned that you achieved an 85% improvement in matching efficiency by engineering a Resume Shortlisting system. Could you provide more details on how you accomplished this?', 'I noticed that you have a leadership position as a Lead for the Google Developer Student Club. What is one valuable lesson you learned while taking on that responsibility?', 'Tell me about a time when you faced a setback or challenge while working on AI projects. How did you handle it, and what did you learn from that experience?', 'Describe a scenario where you had to adapt to changes in a project scope or timeline while working in your internship. How did you manage the adjustments, and what was the outcome?']}]
2025-03-24 22:57:52,223 - api_logger - INFO - Interview Experience Request with Session ID: new-test6 Successfully processed
2025-03-24 22:58:49,180 - api_logger - INFO - Fetching session data for session ID: new-test6 and is_ca: False
2025-03-24 22:58:50,875 - api_logger - INFO - Fetching session data for session_id: new-test6
2025-03-24 22:58:51,438 - api_logger - INFO - Session data fetched successfully for session ID: new-test6
2025-03-24 23:05:44,197 - api_logger - INFO - Request received for JD with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:05:45,990 - api_logger - INFO - Getting file text for: uploaded-data\uploaded_ds_jd.pdf
2025-03-24 23:05:46,005 - api_logger - INFO - Processing PDF file.
2025-03-24 23:05:46,162 - api_logger - INFO - File text extraction completed.
2025-03-24 23:05:46,162 - api_logger - INFO - JD Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:05:46,162 - api_logger - INFO - Processing JD upload for session_id: new-test6
2025-03-24 23:05:46,177 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: jd
2025-03-24 23:05:46,585 - api_logger - INFO - Updating job description for session_id: new-test6
2025-03-24 23:05:47,807 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: jd, status: Inprogress
2025-03-24 23:05:48,338 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 23:05:50,027 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 23:05:54,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 23:05:54,127 - api_logger - INFO - Response received successfully.
2025-03-24 23:05:54,127 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: jd
2025-03-24 23:05:54,623 - api_logger - INFO - JD response generated for session_id: new-test6, response_time: 7.950042724609375s
2025-03-24 23:05:54,623 - api_logger - INFO - Updating status for session_id: new-test6, request_type: jd, status: complete
2025-03-24 23:05:55,156 - api_logger - INFO - Status updated successfully.
2025-03-24 23:06:06,165 - api_logger - INFO - Request received for Resume with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:06:07,780 - api_logger - INFO - Resume Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:06:07,781 - api_logger - INFO - Processing resume for session_id: new-test6
2025-03-24 23:06:07,782 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: resume
2025-03-24 23:06:08,037 - api_logger - INFO - No previous session id found for session_id: new-test6
2025-03-24 23:06:08,037 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: resume, status: Inprogress
2025-03-24 23:06:08,550 - api_logger - INFO - Getting file text for: uploaded-data\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 23:06:08,573 - api_logger - INFO - Processing PDF file.
2025-03-24 23:06:08,622 - api_logger - INFO - File text extraction completed.
2025-03-24 23:06:08,622 - api_logger - INFO - Successfully extracted resume text for session_id: new-test6
2025-03-24 23:06:08,622 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 23:06:10,205 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 23:06:16,742 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 23:06:16,764 - api_logger - INFO - Response received successfully.
2025-03-24 23:06:16,764 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test6
2025-03-24 23:06:16,764 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: resume
2025-03-24 23:06:17,292 - api_logger - INFO - Updating status for session_id: new-test6, request_type: resume, status: complete
2025-03-24 23:06:17,792 - api_logger - INFO - Status updated successfully.
2025-03-24 23:06:17,792 - api_logger - INFO - Resume response generated for session_id: new-test6, response_time: 8ms
2025-03-24 23:16:59,181 - api_logger - INFO - Request received for Resume with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:17:00,992 - api_logger - INFO - Resume Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:17:01,002 - api_logger - INFO - Processing resume for session_id: new-test6
2025-03-24 23:17:01,002 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: resume
2025-03-24 23:17:01,234 - api_logger - INFO - Session ID: new-test6 already exists
2025-03-24 23:17:01,234 - api_logger - INFO - Updating resume for session_id: new-test6
2025-03-24 23:17:02,550 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: resume, status: Inprogress
2025-03-24 23:17:03,069 - api_logger - INFO - Getting file text for: temp\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 23:17:03,089 - api_logger - INFO - Processing PDF file.
2025-03-24 23:17:03,125 - api_logger - INFO - File text extraction completed.
2025-03-24 23:17:03,125 - api_logger - INFO - Successfully extracted resume text for session_id: new-test6
2025-03-24 23:17:03,125 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 23:17:04,816 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 23:17:10,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 23:17:10,603 - api_logger - INFO - Response received successfully.
2025-03-24 23:17:10,603 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test6
2025-03-24 23:17:10,603 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: resume
2025-03-24 23:17:11,116 - api_logger - INFO - Updating status for session_id: new-test6, request_type: resume, status: complete
2025-03-24 23:17:11,613 - api_logger - INFO - Status updated successfully.
2025-03-24 23:17:11,613 - api_logger - INFO - Resume response generated for session_id: new-test6, response_time: 9ms
2025-03-24 23:18:40,544 - api_logger - INFO - Request received for Resume with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:18:42,100 - api_logger - INFO - Resume Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:18:42,117 - api_logger - INFO - Processing resume for session_id: new-test6
2025-03-24 23:18:42,117 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: resume
2025-03-24 23:18:42,383 - api_logger - INFO - Session ID: new-test6 already exists
2025-03-24 23:18:42,383 - api_logger - INFO - Updating resume for session_id: new-test6
2025-03-24 23:18:43,527 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: resume, status: Inprogress
2025-03-24 23:18:44,170 - api_logger - INFO - Getting file text for: temp\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 23:18:44,170 - api_logger - ERROR - Exception during resume processing for session_id: new-test6: [Errno 2] No such file or directory: 'temp\\uploaded_Resume_Ved_Vekhande_IIITV.pdf'
2025-03-24 23:19:20,060 - api_logger - INFO - Request received for Resume with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:19:21,791 - api_logger - INFO - Resume Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:19:21,796 - api_logger - INFO - Processing resume for session_id: new-test6
2025-03-24 23:19:21,796 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: resume
2025-03-24 23:19:22,101 - api_logger - INFO - Session ID: new-test6 already exists
2025-03-24 23:19:22,102 - api_logger - INFO - Updating resume for session_id: new-test6
2025-03-24 23:19:23,366 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: resume, status: Inprogress
2025-03-24 23:19:23,912 - api_logger - INFO - Getting file text for: temp\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 23:19:23,912 - api_logger - ERROR - Exception during resume processing for session_id: new-test6: [Errno 2] No such file or directory: 'temp\\uploaded_Resume_Ved_Vekhande_IIITV.pdf'
2025-03-24 23:19:30,616 - api_logger - INFO - Request received for Resume with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:19:32,318 - api_logger - INFO - Resume Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:19:32,323 - api_logger - INFO - Processing resume for session_id: new-test6
2025-03-24 23:19:32,323 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: resume
2025-03-24 23:19:32,593 - api_logger - INFO - Session ID: new-test6 already exists
2025-03-24 23:19:32,594 - api_logger - INFO - Updating resume for session_id: new-test6
2025-03-24 23:19:33,932 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: resume, status: Inprogress
2025-03-24 23:19:34,440 - api_logger - INFO - Getting file text for: temp\uploaded_Resume_Ved_Vekhande_IIITV.pdf
2025-03-24 23:19:34,440 - api_logger - INFO - Processing PDF file.
2025-03-24 23:19:34,483 - api_logger - INFO - File text extraction completed.
2025-03-24 23:19:34,483 - api_logger - INFO - Successfully extracted resume text for session_id: new-test6
2025-03-24 23:19:34,498 - api_logger - INFO - Fetching latest prompt for request_type: resume
2025-03-24 23:19:36,028 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 23:19:41,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 23:19:41,458 - api_logger - INFO - Response received successfully.
2025-03-24 23:19:41,458 - api_logger - INFO - Succesfully generated resume questions for session_id: new-test6
2025-03-24 23:19:41,459 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: resume
2025-03-24 23:19:42,033 - api_logger - INFO - Updating status for session_id: new-test6, request_type: resume, status: complete
2025-03-24 23:19:42,578 - api_logger - INFO - Status updated successfully.
2025-03-24 23:19:42,578 - api_logger - INFO - Resume response generated for session_id: new-test6, response_time: 9ms
2025-03-24 23:25:29,548 - api_logger - INFO - Request received for JD with Session ID: new-test6 Model: gpt-4o-mini and is_ca: False
2025-03-24 23:25:31,731 - api_logger - INFO - Getting file text for: temp\uploaded_ds_jd.pdf
2025-03-24 23:25:31,731 - api_logger - INFO - Processing PDF file.
2025-03-24 23:25:31,836 - api_logger - INFO - File text extraction completed.
2025-03-24 23:25:31,837 - api_logger - INFO - JD Request with Session ID: new-test6 Successfully received and added to background task
2025-03-24 23:25:31,845 - api_logger - INFO - Processing JD upload for session_id: new-test6
2025-03-24 23:25:31,846 - api_logger - INFO - Checking if session does not exist for session_id: new-test6, request_type: jd
2025-03-24 23:25:32,093 - api_logger - INFO - Updating job description for session_id: new-test6
2025-03-24 23:25:33,294 - api_logger - INFO - Inserting status for session_id: new-test6, request_type: jd, status: Inprogress
2025-03-24 23:25:34,414 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-03-24 23:25:36,468 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-03-24 23:25:40,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-24 23:25:40,202 - api_logger - INFO - Response received successfully.
2025-03-24 23:25:40,203 - api_logger - INFO - Inserting record for session_id: new-test6, request_type: jd
2025-03-24 23:25:40,730 - api_logger - INFO - JD response generated for session_id: new-test6, response_time: 8.35743260383606s
2025-03-24 23:25:40,730 - api_logger - INFO - Updating status for session_id: new-test6, request_type: jd, status: complete
2025-03-24 23:25:41,233 - api_logger - INFO - Status updated successfully.
