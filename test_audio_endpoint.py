"""
Simple test script for the Generate Audio endpoint.
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# API Configuration
BASE_URL = "http://localhost:8001"
API_KEY = os.getenv("API_KEY_NAME", "myinterviewpractice")
API_SECRET = os.getenv("API_SECRET_NAME", "a0a408ef-e2b3-4680-bd58-35dbf66e1a7f")

def test_generate_audio():
    """Test the generate audio endpoint with proper JSON formatting."""
    
    # Headers
    headers = {
        "Content-Type": "application/json",
        "API_KEY": API_KEY,
        "API_SECRET": API_SECRET
    }
    
    # Request data
    request_data = {
        "questions": [
            "Tell me about yourself",
            "What are your strengths?"
        ],
        "voice_id": "21m00Tcm4TlvDq8ikWAM",
        "model": "eleven_flash_v2_5"
    }
    
    print("🧪 Testing Generate Audio Endpoint")
    print("=" * 40)
    print(f"URL: {BASE_URL}/audio/generate-audio")
    print(f"Headers: {headers}")
    print(f"Request Data: {json.dumps(request_data, indent=2)}")
    print("-" * 40)
    
    try:
        # Make the request
        response = requests.post(
            f"{BASE_URL}/audio/generate-audio",
            headers=headers,
            json=request_data,  # Use json parameter instead of data
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ SUCCESS!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return data.get("job_id")
        else:
            print("❌ FAILED!")
            print(f"Response Text: {response.text}")
            try:
                error_data = response.json()
                print(f"Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Cannot connect to server")
        print("Make sure FastAPI server is running: uvicorn api.main:app --reload")
        return None
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return None

def test_voices_endpoint():
    """Test the voices endpoint to make sure the server is working."""
    print("\n🎤 Testing Voices Endpoint")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/audio/voices")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Voices endpoint working!")
            print(f"Available voices: {len(data.get('voices', {}))}")
            return True
        else:
            print(f"❌ Voices endpoint failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing voices: {str(e)}")
        return False

def test_server_health():
    """Test if the server is running and responding."""
    print("🏥 Testing Server Health")
    print("=" * 40)
    
    try:
        # Test the docs endpoint
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ Server is running and responding")
            return True
        else:
            print(f"❌ Server responding but with error: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        print("Start the server with: uvicorn api.main:app --reload")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 AUDIO ENDPOINT TEST SUITE")
    print("=" * 50)
    
    # Step 1: Check server health
    if not test_server_health():
        exit(1)
    
    # Step 2: Test voices endpoint
    if not test_voices_endpoint():
        print("⚠️  Voices endpoint failed, but continuing with main test...")
    
    # Step 3: Test generate audio endpoint
    job_id = test_generate_audio()
    
    if job_id:
        print(f"\n🎉 SUCCESS! Job ID: {job_id}")
        print("\n💡 Next steps:")
        print(f"1. Check job status: GET {BASE_URL}/audio/job-status/{job_id}")
        print(f"2. Monitor progress until completion")
        print(f"3. Download files when ready")
    else:
        print("\n❌ Test failed. Check the error messages above.")
