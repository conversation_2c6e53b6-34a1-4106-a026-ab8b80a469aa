import streamlit as st
import os.path
import tempfile
import numpy as np
import openai
import sys
import json
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from api.utils.function_utils import get_file_text
from api.prompts.prompts import get_jd_initial_message, get_resume_initial_message
from api.utils.qa_model import generate_response
from legacy_files.old_prompts import get_audio_initial_message, get_resume_and_JD_initial_message
st.title("Interview Question Generator")

st.subheader("Upload Resume, Job Description or Candidate Audio")

model = st.selectbox(
    'Select Model',
    ('gpt-3.5-turbo', 'gpt-4'))

options = st.multiselect(
    'Choose from where you want questions to be generated',
    ['Resume', 'JD' , 'Candidate Audio' , 'Resume and JD'])

if 'Resume and JD' in options:
    resume = st.file_uploader("Upload resume",accept_multiple_files=False,type=['pdf','docx'] )
    jd = st.file_uploader("Upload the job description",accept_multiple_files=False,type=['pdf','docx'] , key="resume_jd")
if 'Resume' in options:
    role = st.text_input('Job Role')
    if 'Resume and JD' not in options:
        resume = st.file_uploader("Upload resume",accept_multiple_files=False,type=['pdf','docx'] , key="resume")
if 'JD' in options:
    if 'Resume and JD' not in options:
        jd = st.file_uploader("Upload the job description",accept_multiple_files=False,type=['pdf','docx'] , key="jd")

if 'Candidate Audio' in options:
    uploaded_file = st.file_uploader("Choose a sound file", type=["wav", "mp3", "m4a"], key="audio")

button = st.button("Submit")

if button:
    with st.spinner("Generating questions..."):
        
        if 'Resume and JD' in options:
            if resume:
                resume_text = get_file_text(resume)
                text = "Parsed text from Resume: \n\n"
                text = text + resume_text
                text = text + "\nParsed text from Job Description: \n\n"
                text = text + get_file_text(jd)
                st.subheader("Resume and Job Description")
                expander = st.expander("View parsed resume and JD text", expanded=False)
                expander.code(resume_text)
                messages = get_resume_and_JD_initial_message(text)
                res = generate_response(messages,model)
                expander = st.expander("View Generated Questions", expanded=True)
                if isinstance(res, tuple):
                    res = res[0]
                try:
                    expander.json(json.loads(res))  # Parse as JSON
                except (TypeError, json.JSONDecodeError):
                    expander.write("Error: Invalid JSON format. Displaying raw response.")
                    expander.code(res, language="json")

                expander = st.expander("Prompt used for generating questions", expanded=False)
                expander.write(messages[0]['content'])
        if 'Resume' in options:
            if resume:
                st.subheader("Resume")
                if 'Resume and JD' not in options:
                    resume_text = get_file_text(resume)
                    expander = st.expander("View parsed resume text", expanded=False)
                expander.code(resume_text)
                messages = get_resume_initial_message(resume_text,role)
                res = generate_response(messages,model)
                expander = st.expander("View Generated Questions", expanded=True)
                if isinstance(res, tuple):
                    res = res[0]
                try:
                    expander.json(json.loads(res))  # Parse as JSON
                except (TypeError, json.JSONDecodeError):
                    expander.write("Error: Invalid JSON format. Displaying raw response.")
                    expander.code(res, language="json")
                expander = st.expander("Prompt used for generating questions", expanded=False)
                expander.code(messages[0]['content'])
        if 'JD' in options:
            if 'Resume and JD' not in options:
                if jd:
                    text = get_file_text(jd)
                    st.subheader("Job Description")
                    expander = st.expander("View job description", expanded=False)
                    expander.code(text)
                    messages = get_jd_initial_message(text)
                    res = generate_response(messages,model)
                    expander = st.expander("View Generated Questions", expanded=True)
                    if isinstance(res, tuple):
                        res = res[0]
                    try:
                        expander.json(json.loads(res))  # Parse as JSON
                    except (TypeError, json.JSONDecodeError):
                        expander.write("Error: Invalid JSON format. Displaying raw response.")
                        expander.code(res, language="json")

                    expander = st.expander("Prompt used for generating questions", expanded=False)
                    expander.write(messages[0]['content'])
        if 'Candidate Audio' in options:
            if uploaded_file:
                file_name = uploaded_file.name
                print(file_name.split(".")[-1])
                # Load the sound file
                if file_name.split(".")[-1] in ["wav", "mp3"]:
                    
                    temp_dir = tempfile.mkdtemp()
                    variable = np.random.randint(1111, 1111111)
                    file_name = st.text_input('Enter file name', fr'recording{variable}.m4a')
                    temp_path = os.path.join(temp_dir, file_name)
                    # audio_in = AudioSegment.from_file(uploaded_file.name, format="m4a")
                    with open(temp_path, "wb") as f:
                        f.write(uploaded_file.getvalue())

                    audio_file = open(temp_path, "rb")
                    with st.spinner("Transcribing Audio..."):
                        candiate_notes = openai.Audio.translate("whisper-1", audio_file)["text"]
                    #print(internship_notes)
                    st.subheader("Audio")
                    expander = st.expander("The transcription of the uploaded audio:", expanded=False)
                    expander.code(candiate_notes)
                    messages = get_audio_initial_message(candiate_notes)
                    res = generate_response(messages,model)
                    expander = st.expander("View Generated Questions", expanded=True)
                    if isinstance(res, tuple):
                        res = res[0]
                    try:
                        expander.json(json.loads(res))  # Parse as JSON
                    except (TypeError, json.JSONDecodeError):
                        expander.write("Error: Invalid JSON format. Displaying raw response.")
                        expander.code(res, language="json")
                    expander = st.expander("Prompt used for generating questions", expanded=False)
                    expander.write(messages[0]['content'])

 
