2025-04-09 23:22:56,041 - api_logger - INFO - Fetching session data for session ID: new-test9 and is_ca: False
2025-04-09 23:22:56,103 - api_logger - ERROR - Exception: 'Client' object has no attribute 'trace' Occurred while fetching session data
2025-04-09 23:22:56,103 - api_logger - INFO - Session data fetched successfully for session ID: new-test9
2025-04-09 23:26:08,713 - api_logger - INFO - Fetching session data for session ID: new-test9 and is_ca: False
2025-04-09 23:26:15,542 - api_logger - INFO - Fetching session data for session_id: new-test9
2025-04-09 23:26:16,272 - api_logger - INFO - Session data fetched successfully for session ID: new-test9
2025-04-09 23:41:11,518 - api_logger - INFO - Request received for Job Title with Session ID: ved_test1 Model: gpt-4o-mini and is_ca: False and Experience Level: Mid and Number of Questions: 10
2025-04-09 23:41:13,289 - api_logger - INFO - Job Title Request with Session ID: ved_test1 Successfully received and added to background task
2025-04-09 23:41:13,294 - api_logger - INFO - Starting background response generation for session_id: ved_test1, job_title: Data Scientist
2025-04-09 23:41:13,296 - api_logger - INFO - Checking if session does not exist for session_id: ved_test1, request_type: job_title
2025-04-09 23:41:13,553 - api_logger - INFO - No previous session id
2025-04-09 23:41:13,553 - api_logger - INFO - Inserting status for session_id: ved_test1, request_type: job_title, status: Inprogress
2025-04-09 23:41:14,270 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-09 23:41:16,078 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-09 23:41:23,979 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-09 23:41:24,040 - api_logger - INFO - Response received successfully.
2025-04-09 23:41:24,040 - api_logger - INFO - Succesfully generated job title questions for session_id: ved_test1
2025-04-09 23:41:24,040 - api_logger - INFO - Inserting record for session_id: ved_test1, request_type: job_title
2025-04-09 23:41:24,629 - api_logger - INFO - Inserted initial status for session_id: ved_test1
2025-04-09 23:41:24,633 - api_logger - INFO - Updating status for session_id: ved_test1, request_type: job_title, status: complete
2025-04-09 23:41:25,380 - api_logger - INFO - Status updated successfully.
2025-04-09 23:41:25,380 - api_logger - INFO - Response generated for session_id: ved_test1, response_time: 10ms
2025-04-09 23:58:35,280 - api_logger - INFO - Request received for Job Title with Session ID: ved_test1 Model: gpt-4o-mini and is_ca: False and Experience Level: Mid and Number of Questions: 10
2025-04-09 23:58:37,159 - api_logger - INFO - Job Title Request with Session ID: ved_test1 Successfully received and added to background task
2025-04-09 23:58:37,165 - api_logger - INFO - Starting background response generation for session_id: ved_test1, job_title: Data Scientist
2025-04-09 23:58:37,166 - api_logger - INFO - Checking if session does not exist for session_id: ved_test1, request_type: job_title
2025-04-09 23:58:37,440 - api_logger - INFO - Updating job experience for session_id: ved_test1
2025-04-09 23:58:38,404 - api_logger - INFO - Inserting status for session_id: ved_test1, request_type: job_title, status: Inprogress
2025-04-09 23:58:39,102 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-09 23:58:41,049 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-09 23:58:52,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-09 23:58:52,880 - api_logger - INFO - Response received successfully.
2025-04-09 23:58:52,880 - api_logger - INFO - Succesfully generated job title questions for session_id: ved_test1
2025-04-09 23:58:52,880 - api_logger - INFO - Inserting record for session_id: ved_test1, request_type: job_title
2025-04-09 23:58:53,639 - api_logger - INFO - Inserted initial status for session_id: ved_test1
2025-04-09 23:58:53,639 - api_logger - INFO - Updating status for session_id: ved_test1, request_type: job_title, status: complete
2025-04-09 23:58:54,258 - api_logger - INFO - Status updated successfully.
2025-04-09 23:58:54,259 - api_logger - INFO - Response generated for session_id: ved_test1, response_time: 15ms
