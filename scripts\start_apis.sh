#!/bin/bash
BASE_DIR="/home/<USER>/resume_apis"

cd $BASE_DIR

source myenv/bin/activate

PID=$(lsof -t -i:8001)

if [[ ! -z "$PID" ]]; then
    if kill -0 $PID > /dev/null 2>&1; then
            kill -9 $PID
            sleep 2
            echo "" > nohup.out
            echo "Restarting FastAPI Server on port 8001"
    else
            echo "Process $PID not found. Starting a new FastAPI Server on port 8001."
    fi
fi

nohup myenv/bin/uvicorn api.main:app --host 0.0.0.0 --port 8001 --workers 2 > nohup.out 2>&1 &

