"""
This module provides functions to interact with feedback examples in the database.
It includes functionalities to create the table, fetch, add, delete, and manage feedback examples.
"""

import csv
import io
from typing import List, Tuple
from api.common.api_logger import api_logger as logger
from api.common.pydantic_models import FeedbackExample
from api.database.connection_db import get_connection


def load_feedback_examples() -> List[FeedbackExample]:
    """
    Load feedback examples from the database.

    Returns:
        List[FeedbackExample]: List of feedback examples
    """

    connection = None
    try:
        connection = get_connection(is_ca=False)
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM feedback_examples ORDER BY id")
            rows = cursor.fetchall()

            examples = []
            for row in rows:
                example = FeedbackExample(
                    id=str(row['id']),
                    question=row['question'],
                    answer=row['answer'],
                    suggestions=row['suggestions'],
                    proposed_answer=row['proposed_answer']
                )
                examples.append(example)

            logger.info(f"Loaded {len(examples)} feedback examples from database")
            return examples
    except Exception as e:
        logger.error(f"Error loading feedback examples from database: {e}")
        return []
    finally:
        if connection:
            connection.close()

def add_feedback_example(example: FeedbackExample) -> Tuple[bool, FeedbackExample]:
    """
    Add a new feedback example to the database.

    Args:
        example (FeedbackExample): The example to add

    Returns:
        Tuple[bool, FeedbackExample]: (success status, added example with ID)
    """

    connection = None
    try:
        connection = get_connection(is_ca=False)
        with connection.cursor() as cursor:
            # Insert the new example
            cursor.execute("""
                INSERT INTO feedback_examples (question, answer, suggestions, proposed_answer)
                VALUES (%s, %s, %s, %s)
            """, (
                example.question,
                example.answer,
                example.suggestions,
                example.proposed_answer
            ))
            connection.commit()

            # Get the ID of the inserted row
            last_id = cursor.lastrowid

            # Create a copy with the new ID
            example_with_id = example.model_copy()
            example_with_id.id = str(last_id)

            logger.info(f"Added feedback example with ID {last_id} to database")
            return True, example_with_id
    except Exception as e:
        logger.error(f"Error adding feedback example to database: {e}")
        return False, example
    finally:
        if connection:
            connection.close()

def delete_feedback_example(example_id: str) -> bool:
    """
    Delete a feedback example from the database.

    Args:
        example_id (str): The unique ID of the example to delete

    Returns:
        bool: True if deletion was successful, False if not found or error
    """

    connection = None
    try:
        connection = get_connection(is_ca=False)
        with connection.cursor() as cursor:
            # Delete the example
            cursor.execute("DELETE FROM feedback_examples WHERE id = %s", (example_id,))
            connection.commit()

            # Check if any rows were affected
            if cursor.rowcount == 0:
                logger.info(f"No feedback example found with ID: {example_id} in database")
                return False

            logger.info(f"Deleted feedback example with ID {example_id} from database")
            return True
    except Exception as e:
        logger.error(f"Error deleting feedback example from database: {e}")
        return False
    finally:
        if connection:
            connection.close()

def export_feedback_examples_to_csv() -> Tuple[bool, io.StringIO]:
    """
    Export all feedback examples to CSV format.

    Returns:
        Tuple[bool, io.StringIO]: (success status, CSV data as StringIO)
    """
    connection = None
    try:
        # Get examples directly from the database
        connection = get_connection(is_ca=False)

        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['question', 'answer', 'suggestions', 'proposed_answer'])

        # Query the database
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM feedback_examples ORDER BY id")
            rows = cursor.fetchall()

            if not rows:
                logger.info("No feedback examples to export")
                output.seek(0)
                return True, output

            # Write data rows
            for row in rows:
                writer.writerow([
                    row['question'],
                    row['answer'],
                    row['suggestions'],
                    row['proposed_answer']
                ])

        output.seek(0)  # Reset file pointer to beginning
        logger.info(f"Successfully exported {len(rows)} feedback examples to CSV")
        return True, output
    except Exception as e:
        logger.error(f"Error exporting feedback examples to CSV: {e}")
        return False, io.StringIO()
    finally:
        if connection:
            connection.close()

def import_feedback_examples_from_csv(csv_file: io.BytesIO) -> Tuple[bool, int]:
    """
    Import feedback examples from a CSV file.
    This will overwrite all existing feedback examples with the ones from the CSV.

    Args:
        csv_file (io.BytesIO): CSV file content as BytesIO

    Returns:
        Tuple[bool, int]: (success status, number of examples imported)
    """
    connection = None
    try:
        connection = get_connection(is_ca=False)

        # Decode bytes to string
        content = csv_file.read().decode('utf-8')
        csv_file = io.StringIO(content)

        # Parse CSV
        reader = csv.DictReader(csv_file)

        # Check if required columns exist
        required_columns = ['question', 'answer', 'suggestions', 'proposed_answer']
        if not all(col in reader.fieldnames for col in required_columns):
            missing = [col for col in required_columns if col not in reader.fieldnames]
            logger.error(f"CSV is missing required columns: {missing}")
            return False, 0

        # Clear existing examples
        with connection.cursor() as cursor:
            # First count how many examples we're replacing
            cursor.execute("SELECT COUNT(*) as count FROM feedback_examples")
            count_result = cursor.fetchone()
            existing_count = count_result['count'] if count_result else 0

            # Then truncate the table
            cursor.execute("TRUNCATE TABLE feedback_examples")
            connection.commit()
            logger.info(f"Cleared {existing_count} existing feedback examples from database")

        # Process rows
        count = 0

        for row in reader:
            try:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO feedback_examples (question, answer, suggestions, proposed_answer)
                        VALUES (%s, %s, %s, %s)
                    """, (
                        row['question'],
                        row['answer'],
                        row['suggestions'],
                        row['proposed_answer']
                    ))
                    connection.commit()
                    count += 1
            except Exception as e:
                logger.error(f"Error importing row from CSV: {e}")

        logger.info(f"Successfully imported {count} feedback examples from CSV (replaced all existing examples)")
        return True, count
    except Exception as e:
        logger.error(f"Error importing feedback examples from CSV: {e}")
        return False, 0
    finally:
        if connection:
            connection.close()
