import requests

# Create test for upload_job_title_experience_level
import os
from dotenv import load_dotenv
load_dotenv()
API_KEY_NAME = os.getenv("API_KEY_NAME")
API_KEY_SECRET = os.getenv("API_SECRET_NAME")


def test_upload_job_title_experience_level():
    url = "http://35.175.232.242:8001/upload_job_title_experience_level/"
    # use above curl command and create request
    params= {
        "session_id": "123224",
        "job_title": "Data scientist",
        "experience_level": "4",
        "API_SECRET": API_KEY_SECRET
    }
    headers = {
        "accept": "application/json",
        "API_KEY": API_KEY_NAME,
        "API_SECRET": API_KEY_SECRET
        }

    response = requests.request("POST", url, params=params, headers=headers)
    
    print(response.json())

test_upload_job_title_experience_level()