"""
This module handles the interview experience-related endpoints. It provides functionality to get interview experiences and session data.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
import time
from api.common.dependencies import get_api_key
from api.database.connection_db import get_connection
from api.database.session_db import fetch_session_data
from api.database.job_status_db import are_all_tasks_complete
from api.utils.processing_functions import process_interview 
from api.common.pydantic_models import GPTModels, SessionData
from typing import List
from api.common.api_logger import api_logger as logger

router = APIRouter()

@router.post("/get_interview_experience/")
def get_interview_experience(session_id: str, model: GPTModels = Query(...), is_ca: bool = Query(False), token: bool = Depends(get_api_key)):
    """
    Fetches interview experience based on session ID and model.
    """
    try:
        logger.info(f"Request received for Interview Experience with Session ID: {session_id} Model: {model.value} and is_ca: {is_ca}")
        start_time = time.time()
        connection = get_connection(is_ca)
        model = model.value
        if model=="gpt-3.5-turbo" or model == "gpt-3.5-turbo-1106":
            model="gpt-4o-mini"
        elif model == "gpt-4-turbo-preview" or model =="gpt-4-1106-preview":
            model = "gpt-4o"
        logger.info(f"Processing request for Interview Experience with Session ID: {session_id} Model: {model} and is_ca: {is_ca}")

        while not are_all_tasks_complete(session_id, connection):
            if time.time() - start_time > 30:  # Check if 1 minute has passed
                logger.info(f"Timeout reached, proceeding with incomplete tasks for session ID: {session_id}")
                break
            time.sleep(2)
            try:
                connection.close()
                connection = get_connection(is_ca)
            except Exception as e:
                logger.error(f"Exception: {e} Occurred while refreshing connection for session ID: {session_id}")

        logger.info(f"Proceeding with tasks completion status for session ID: {session_id}")
        result = process_interview(session_id, model, connection)
        logger.info(f"Interview Experience Request with Session ID: {session_id} Successfully processed")
        return result
    except Exception as e:
        logger.error(f"Exception: {e} Occurred while processing interview experience request for session ID: {session_id}")
        return {"status": False, "error": str(e)}
    

@router.get("/get_session_data/{session_id}", response_model=List[SessionData])
def get_session_data(session_id: str, is_ca: bool = Query(False), token: bool = Depends(get_api_key)):
    """
    Retrieves session data for a given session ID.
    """
    logger.info(f"Fetching session data for session ID: {session_id} and is_ca: {is_ca}")
    try:
        connection = get_connection(is_ca)
        session_data = fetch_session_data(session_id, connection)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")
    except Exception as e:
        logger.error(f"Exception: {e} Occurred while fetching session data")
        session_data = []
    logger.info(f"Session data fetched successfully for session ID: {session_id}")
    return session_data