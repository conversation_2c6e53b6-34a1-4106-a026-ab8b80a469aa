2025-05-23 11:58:46,586 - api_logger - ERROR - CSV is missing required columns: ['question', 'answer', 'suggestions', 'proposed_answer']
2025-05-23 12:01:08,078 - api_logger - INFO - Saved 3 feedback examples to C:\Users\<USER>\Documents\task\raj_resume\data\feedback_examples.json
2025-05-23 12:01:08,096 - api_logger - INFO - Saved 4 feedback examples to C:\Users\<USER>\Documents\task\raj_resume\data\feedback_examples.json
2025-05-23 12:01:08,105 - api_logger - INFO - Saved 5 feedback examples to C:\Users\<USER>\Documents\task\raj_resume\data\feedback_examples.json
2025-05-23 12:01:08,105 - api_logger - INFO - Successfully imported 3 feedback examples from CSV
2025-05-23 12:01:36,611 - api_logger - INFO - Successfully exported 5 feedback examples to CSV
2025-05-23 12:02:05,973 - api_logger - INFO - Successfully exported 5 feedback examples to CSV
2025-05-23 12:07:54,210 - api_logger - INFO - Found 5 feedback examples in JSON file.
2025-05-23 12:07:58,288 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:10:59,342 - api_logger - INFO - Found 5 feedback examples in JSON file.
2025-05-23 12:11:03,428 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:07,480 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:11,559 - api_logger - ERROR - Error adding feedback example to database: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:15,612 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:19,678 - api_logger - ERROR - Error adding feedback example to database: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:23,751 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:27,786 - api_logger - ERROR - Error adding feedback example to database: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:31,896 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:13:32,426 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:14:03,182 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:14:07,231 - api_logger - ERROR - Error importing feedback examples from CSV: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:16:09,309 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:16:09,309 - api_logger - WARNING - Switching to JSON file fallback mode
2025-05-23 12:16:41,366 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:16:41,366 - api_logger - WARNING - Switching to JSON file fallback mode
2025-05-23 12:29:14,415 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:36:03,408 - api_logger - ERROR - Error checking if feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:41:30,636 - api_logger - ERROR - Error checking if feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:41:34,706 - api_logger - ERROR - Error importing feedback examples from CSV: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 13:09:52,000 - api_logger - ERROR - Error importing feedback examples from CSV: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 13:21:25,858 - api_logger - INFO - Cleared 0 existing feedback examples from database
2025-05-23 13:21:27,597 - api_logger - INFO - Successfully imported 3 feedback examples from CSV (replaced all existing examples)
2025-05-23 13:23:56,491 - api_logger - INFO - Successfully exported 3 feedback examples to CSV
2025-05-23 13:25:59,065 - api_logger - INFO - Added feedback example with ID 4 to database
2025-05-23 13:26:17,906 - api_logger - INFO - Loaded 4 feedback examples from database
2025-05-23 13:31:50,629 - api_logger - INFO - Cleared 4 existing feedback examples from database
2025-05-23 13:31:52,452 - api_logger - INFO - Successfully imported 3 feedback examples from CSV (replaced all existing examples)
2025-05-23 13:32:11,657 - api_logger - INFO - Successfully exported 3 feedback examples to CSV
2025-05-23 13:33:13,964 - api_logger - INFO - Added feedback example with ID 4 to database
2025-05-23 13:33:32,396 - api_logger - INFO - Loaded 4 feedback examples from database
2025-05-23 13:34:11,289 - api_logger - INFO - Cleared 4 existing feedback examples from database
2025-05-23 13:34:12,719 - api_logger - INFO - Successfully imported 3 feedback examples from CSV (replaced all existing examples)
2025-05-23 13:34:25,031 - api_logger - INFO - Successfully exported 3 feedback examples to CSV
2025-05-23 13:35:21,455 - api_logger - INFO - Loaded 3 feedback examples from database
