2025-05-23 11:58:46,586 - api_logger - ERROR - CSV is missing required columns: ['question', 'answer', 'suggestions', 'proposed_answer']
2025-05-23 12:01:08,078 - api_logger - INFO - Saved 3 feedback examples to C:\Users\<USER>\Documents\task\raj_resume\data\feedback_examples.json
2025-05-23 12:01:08,096 - api_logger - INFO - Saved 4 feedback examples to C:\Users\<USER>\Documents\task\raj_resume\data\feedback_examples.json
2025-05-23 12:01:08,105 - api_logger - INFO - Saved 5 feedback examples to C:\Users\<USER>\Documents\task\raj_resume\data\feedback_examples.json
2025-05-23 12:01:08,105 - api_logger - INFO - Successfully imported 3 feedback examples from CSV
2025-05-23 12:01:36,611 - api_logger - INFO - Successfully exported 5 feedback examples to CSV
2025-05-23 12:02:05,973 - api_logger - INFO - Successfully exported 5 feedback examples to CSV
2025-05-23 12:07:54,210 - api_logger - INFO - Found 5 feedback examples in JSON file.
2025-05-23 12:07:58,288 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:10:59,342 - api_logger - INFO - Found 5 feedback examples in JSON file.
2025-05-23 12:11:03,428 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:07,480 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:11,559 - api_logger - ERROR - Error adding feedback example to database: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:15,612 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:19,678 - api_logger - ERROR - Error adding feedback example to database: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:23,751 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:27,786 - api_logger - ERROR - Error adding feedback example to database: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:11:31,896 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:13:32,426 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:14:03,182 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:14:07,231 - api_logger - ERROR - Error importing feedback examples from CSV: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:16:09,309 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:16:09,309 - api_logger - WARNING - Switching to JSON file fallback mode
2025-05-23 12:16:41,366 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:16:41,366 - api_logger - WARNING - Switching to JSON file fallback mode
2025-05-23 12:29:14,415 - api_logger - ERROR - Error ensuring feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:36:03,408 - api_logger - ERROR - Error checking if feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:41:30,636 - api_logger - ERROR - Error checking if feedback_examples table exists: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 12:41:34,706 - api_logger - ERROR - Error importing feedback examples from CSV: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
2025-05-23 13:09:52,000 - api_logger - ERROR - Error importing feedback examples from CSV: (2003, "Can't connect to MySQL server on 'localhost' ([WinError 10061] No connection could be made because the target machine actively refused it)")
