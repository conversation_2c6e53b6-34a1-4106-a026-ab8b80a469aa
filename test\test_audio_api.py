"""
Test script for the Audio Generation API.
Demonstrates how to use the TTS endpoints.
"""

import requests
import time
import os
from dotenv import load_dotenv

load_dotenv()

# API Configuration
BASE_URL = "http://localhost:8001"
API_KEY = os.getenv("API_KEY_NAME", "myinterviewpractice")
API_SECRET = os.getenv("API_SECRET_NAME", "a0a408ef-e2b3-4680-bd58-35dbf66e1a7f")

headers = {
    "Content-Type": "application/json",
    "API_KEY": API_KEY,
    "API_SECRET": API_SECRET
}

def test_get_available_voices():
    """Test getting available voices."""
    print("🎤 Testing: Get Available Voices")
    
    response = requests.get(f"{BASE_URL}/audio/voices")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Available voices retrieved successfully")
        print(f"   Default model: {data['default_model']}")
        print(f"   Available voices: {len(data['voices'])}")
        for voice_id, description in data['voices'].items():
            print(f"   - {description} ({voice_id})")
        return True
    else:
        print(f"❌ Failed to get voices: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def test_generate_audio():
    """Test audio generation with sample questions."""
    print("\n🎵 Testing: Generate Audio")
    
    # Sample interview questions
    questions = [
        "Tell me about yourself and your background.",
        "What are your greatest strengths?",
        "Where do you see yourself in five years?"
    ]
    
    request_data = {
        "questions": questions,
        "voice_id": "RACHEL",  # Asian Women voice
        "model": "eleven_flash_v2_5"
    }
    
    response = requests.post(
        f"{BASE_URL}/audio/generate-audio",
        json=request_data,
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        job_id = data["job_id"]
        print(f"✅ Audio generation job created successfully")
        print(f"   Job ID: {job_id}")
        print(f"   Status: {data['status']}")
        print(f"   Total questions: {data['total_questions']}")
        print(f"   Voice used: {data['voice_used']}")
        return job_id
    else:
        print(f"❌ Failed to create audio generation job: {response.status_code}")
        print(f"   Error: {response.text}")
        return None

def test_job_status(job_id):
    """Test job status checking and wait for completion."""
    print(f"\n📊 Testing: Job Status Monitoring for {job_id}")
    
    max_attempts = 30  # Maximum 60 seconds (30 * 2 seconds)
    attempt = 0
    
    while attempt < max_attempts:
        response = requests.get(f"{BASE_URL}/audio/job-status/{job_id}")
        
        if response.status_code == 200:
            data = response.json()
            status = data["status"]
            progress = data["progress"]
            completed_files = len(data["audio_files"])
            
            print(f"   Status: {status} | Progress: {progress}% | Files: {completed_files}")
            
            if status == "completed":
                print("✅ Job completed successfully!")
                return data["audio_files"]
            elif status == "failed":
                error_msg = data.get("error_message", "Unknown error")
                print(f"❌ Job failed: {error_msg}")
                return None
            elif status in ["partial"]:
                print(f"⚠️  Job partially completed: {data.get('error_message', '')}")
                return data["audio_files"]
            
            # Wait before next check
            time.sleep(2)
            attempt += 1
        else:
            print(f"❌ Failed to get job status: {response.status_code}")
            return None
    
    print("⏰ Timeout waiting for job completion")
    return None

def test_download_audio_file(job_id, filename):
    """Test downloading an audio file."""
    print(f"\n⬇️  Testing: Download Audio File {filename}")
    
    response = requests.get(f"{BASE_URL}/audio/download/{job_id}/{filename}")
    
    if response.status_code == 200:
        # Save file locally
        output_path = f"test_audio_{filename}"
        with open(output_path, "wb") as f:
            f.write(response.content)
        
        file_size = len(response.content)
        print(f"✅ Audio file downloaded successfully")
        print(f"   File: {output_path}")
        print(f"   Size: {file_size} bytes")
        return True
    else:
        print(f"❌ Failed to download audio file: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def test_delete_job(job_id):
    """Test deleting a job."""
    print(f"\n🗑️  Testing: Delete Job {job_id}")
    
    response = requests.delete(
        f"{BASE_URL}/audio/job/{job_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Job deleted successfully: {data['message']}")
        return True
    else:
        print(f"❌ Failed to delete job: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def run_full_test():
    """Run complete test suite for audio generation API."""
    print("🚀 Starting Audio Generation API Test Suite")
    print("=" * 50)
    
    # Test 1: Get available voices
    if not test_get_available_voices():
        print("❌ Test suite failed at voice retrieval")
        return
    
    # Test 2: Generate audio
    job_id = test_generate_audio()
    if not job_id:
        print("❌ Test suite failed at audio generation")
        return
    
    # Test 3: Monitor job status
    audio_files = test_job_status(job_id)
    if not audio_files:
        print("❌ Test suite failed at job monitoring")
        return
    
    # Test 4: Download first audio file
    if audio_files:
        first_file = audio_files[0]
        filename = first_file["file_url"].split("/")[-1]
        if not test_download_audio_file(job_id, filename):
            print("❌ Test suite failed at file download")
    
    # Test 5: Clean up - delete job
    test_delete_job(job_id)
    
    print("\n🎉 Audio Generation API Test Suite Completed!")
    print("=" * 50)

if __name__ == "__main__":
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/audio/voices")
        if response.status_code == 200:
            run_full_test()
        else:
            print("❌ Server not responding correctly. Please check if the API is running.")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start the FastAPI server first:")
        print("   uvicorn api.main:app --reload")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
