2025-04-10 01:12:06,122 - api_logger - INFO - Request received for Job Title with Session ID: ved_test1 Model: gpt-4o-mini and is_ca: False and Experience Level: Mid and Number of Questions: 10
2025-04-10 01:12:13,672 - api_logger - INFO - Job Title Request with Session ID: ved_test1 Successfully received and added to background task
2025-04-10 01:12:13,676 - api_logger - INFO - Starting background response generation for session_id: ved_test1, job_title: Data Scientist
2025-04-10 01:12:13,676 - api_logger - INFO - Checking if session does not exist for session_id: ved_test1, request_type: job_title
2025-04-10 01:12:13,960 - api_logger - INFO - Updating job experience for session_id: ved_test1
2025-04-10 01:12:14,910 - api_logger - INFO - Inserting status for session_id: ved_test1, request_type: job_title, status: Inprogress
2025-04-10 01:12:15,401 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-10 01:12:17,632 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-10 01:12:26,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-10 01:12:26,951 - api_logger - INFO - Response received successfully.
2025-04-10 01:12:26,951 - api_logger - INFO - Succesfully generated job title questions for session_id: ved_test1
2025-04-10 01:12:26,951 - api_logger - INFO - Inserting record for session_id: ved_test1, request_type: job_title
2025-04-10 01:12:27,485 - api_logger - INFO - Inserted initial status for session_id: ved_test1
2025-04-10 01:12:27,485 - api_logger - INFO - Updating status for session_id: ved_test1, request_type: job_title, status: complete
2025-04-10 01:12:28,005 - api_logger - INFO - Status updated successfully.
2025-04-10 01:12:28,005 - api_logger - INFO - Response generated for session_id: ved_test1, response_time: 13ms
2025-04-10 01:16:18,483 - api_logger - INFO - Request received for Job Title with Session ID: ved_test1 Model: gpt-4o-mini and is_ca: False and Experience Level: Mid and Number of Questions: 10
2025-04-10 01:16:20,316 - api_logger - INFO - Job Title Request with Session ID: ved_test1 Successfully received and added to background task
2025-04-10 01:16:20,326 - api_logger - INFO - Starting background response generation for session_id: ved_test1, job_title: Data Scientist
2025-04-10 01:16:20,326 - api_logger - INFO - Checking if session does not exist for session_id: ved_test1, request_type: job_title
2025-04-10 01:16:20,579 - api_logger - INFO - Updating job experience for session_id: ved_test1
2025-04-10 01:16:21,609 - api_logger - INFO - Inserting status for session_id: ved_test1, request_type: job_title, status: Inprogress
2025-04-10 01:16:22,186 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-10 01:16:24,013 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-10 01:16:28,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-10 01:16:28,541 - api_logger - INFO - Response received successfully.
2025-04-10 01:16:28,541 - api_logger - INFO - Succesfully generated job title questions for session_id: ved_test1
2025-04-10 01:16:28,541 - api_logger - INFO - Inserting record for session_id: ved_test1, request_type: job_title
2025-04-10 01:16:29,070 - api_logger - INFO - Inserted initial status for session_id: ved_test1
2025-04-10 01:16:29,070 - api_logger - INFO - Updating status for session_id: ved_test1, request_type: job_title, status: complete
2025-04-10 01:16:29,596 - api_logger - INFO - Status updated successfully.
2025-04-10 01:16:29,597 - api_logger - INFO - Response generated for session_id: ved_test1, response_time: 8ms
2025-04-10 01:24:02,443 - api_logger - INFO - Request received for Job Title with Session ID: ved_test1 Model: gpt-4o-mini and is_ca: False and Experience Level: Mid and Number of Questions: 10
2025-04-10 01:24:04,324 - api_logger - INFO - Job Title Request with Session ID: ved_test1 Successfully received and added to background task
2025-04-10 01:24:04,324 - api_logger - INFO - Starting background response generation for session_id: ved_test1, job_title: Data Scientist
2025-04-10 01:24:04,324 - api_logger - INFO - Checking if session does not exist for session_id: ved_test1, request_type: job_title
2025-04-10 01:24:04,640 - api_logger - INFO - Updating job experience for session_id: ved_test1
2025-04-10 01:24:05,589 - api_logger - INFO - Inserting status for session_id: ved_test1, request_type: job_title, status: Inprogress
2025-04-10 01:24:06,246 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-10 01:24:08,015 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-10 01:24:12,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-10 01:24:12,976 - api_logger - INFO - Response received successfully.
2025-04-10 01:24:12,976 - api_logger - INFO - Succesfully generated job title questions for session_id: ved_test1
2025-04-10 01:24:12,976 - api_logger - INFO - Inserting record for session_id: ved_test1, request_type: job_title
2025-04-10 01:24:13,555 - api_logger - INFO - Inserted initial status for session_id: ved_test1
2025-04-10 01:24:13,555 - api_logger - INFO - Updating status for session_id: ved_test1, request_type: job_title, status: complete
2025-04-10 01:24:14,182 - api_logger - INFO - Status updated successfully.
2025-04-10 01:24:14,182 - api_logger - INFO - Response generated for session_id: ved_test1, response_time: 8ms
