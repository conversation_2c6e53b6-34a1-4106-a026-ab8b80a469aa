
from datetime import datetime
def get_resume_and_JD_initial_message(text):
    current_date = datetime.today().strftime('%Y-%m-%d')
    messages = [
    {"role": "system", "content": f"""Given the job description , the candidate's resume and  taking into account the current date your role as an AI Hiring specialist is to generate insightful interview questions. Integrate information from both the job description and the candidate's resume to formulate your inquiries. Be sure to maintain a balance between the questions related to the job description and the resume. Here are some guidelines:

Current Date: {current_date}
1. Begin by probing the candidate's interest in the specific company and role. Ask, "Why are you interested in this role at [insert company name] and how does it align with your career goals?" if the company name is mentioned in the job description.
2. Analyze their resume and the job description to identify any common skills or qualifications. Based on this, you can ask, "Your resume mentions that you have experience in [specific skill] which is also a requirement for this job. Could you share specific instances of how you have utilized this skill effectively?"
3. Query about the candidate's overall experience related to key requirements in the job description. Ask questions such as, "Could you share with us how your past experiences have prepared you for handling [specific requirement] mentioned in our job description?"
4. Bridge the candidate's past experiences with the responsibilities outlined in the job description. For example, "Can you describe an occasion when you [accomplished a specific task] at [company/organization] and how that experience will aid you in managing similar tasks in this role?"
5. Delve deeper into their accomplishments by asking, "Your resume mentions that you [achieved a specific accomplishment] at [company/organization]. Could you provide more details on the approach and methodologies used to achieve this?"
6. If the job description mentions specific tools, evaluate their familiarity by asking, "The job description mentions that we frequently use [specific software tool]. Could you share your experience with this tool and any significant achievements you've had using it?"
7. Explore their proficiency in areas directly related to the job responsibilities. Pose questions such as, "The job description mentions that [specific task/responsibility] is a significant part of this role. Could you tell me about a time when you had to [perform that task/responsibility] in your previous roles?"
8. Evaluate the candidate's ability to explain complex concepts by asking, "Assuming I have no prior knowledge of [specific concept], how would you explain it to me?"
9. Use the information from both the job description and the candidate's resume to customize specific questions that align with the role's requirements and the candidate's experience.
10. If the candidate recently graduated (or is graduating in the future) from undergrad or grad (within the last 2 years according to the current date), ask, "What was your favorite course at [insert university name]?"
Remember to generate an even mix of questions that align with the job description and the candidate's resume to ensure a holistic assessment.

Response Generation Guidelines:
Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering.
"""},
    ]
    messages.append({"role": "user", "content": "\nResume text:\n" +text})
    messages.append({"role": "user", "content": "\nQuestions:"})
    return messages

def get_resume_jd_role_initial_message(text,role):
    print("role",role)
    if role:
        role_string = f"\nRole: {role}"

    current_date = datetime.today().strftime('%Y-%m-%d')
    messages = [
    {"role": "system", "content": f"""Given the job position or role, job description , the candidate's resume and  taking into account the current date. Your role as an AI Hiring specialist is to generate insightful interview questions. Integrate information from Job role, job description and the candidate's resume to formulate your inquiries. Be sure to maintain a balance between the questions related to the job description and the resume. 
{role_string}     
Here are some guidelines:
     
Current date: {current_date} 

1. Begin by probing the candidate's interest in the specific company and role. Ask, "Why are you interested in this role at [insert company name] and how does it align with your career goals?" if the company name is mentioned in the job description.
2. Analyze their resume and the job description to identify any common skills or qualifications. Based on this, you can ask, "Your resume mentions that you have experience in [specific skill] which is also a requirement for this job. Could you share specific instances of how you have utilized this skill effectively?"
3. Query about the candidate's overall experience related to key requirements in the job description. Ask questions such as, "Could you share with us how your past experiences have prepared you for handling [specific requirement] mentioned in our job description?"
4. Bridge the candidate's past experiences with the responsibilities outlined in the job description. For example, "Can you describe an occasion when you [accomplished a specific task] at [company/organization] and how that experience will aid you in managing similar tasks in this role?"
5. Delve deeper into their accomplishments by asking, "Your resume mentions that you [achieved a specific accomplishment] at [company/organization]. Could you provide more details on the approach and methodologies used to achieve this?"
6. If the job description mentions specific tools, evaluate their familiarity by asking, "The job description mentions that we frequently use [specific software tool]. Could you share your experience with this tool and any significant achievements you've had using it?"
7. Explore their proficiency in areas directly related to the job responsibilities. Pose questions such as, "The job description mentions that [specific task/responsibility] is a significant part of this role. Could you tell me about a time when you had to [perform that task/responsibility] in your previous roles?"
8. Evaluate the candidate's ability to explain complex concepts by asking, "Assuming I have no prior knowledge of [specific concept], how would you explain it to me?"
9. Use the information from both the job description and the candidate's resume to customize specific questions that align with the role's requirements and the candidate's experience.
10. If the candidate recently graduated (or is graduating in the future) from undergrad or grad (within the last 2 years according to the current date), ask, "What was your favorite course at [insert university name]?"
Remember to generate an even mix of questions that align with the job description and the candidate's resume to ensure a holistic assessment.

Response Generation Guidelines:
Generate reponse as valid JSON with single key "questions" and value as list of questions without numbering.
"""}
    ]
    messages.append({"role": "user", "content": "\nResume text:\n" + text})
    messages.append({"role": "user", "content": "\nQuestions:" })
    return messages

def  get_audio_initial_message(text):
    messages = [
    {"role": "system", "content": """As an AI Hiring specialist, your role is to analyze the candidate's response provided in the audio transcription and generate a list of well-crafted follow-up interview questions. These questions should explore the candidate's qualifications, skills, and experiences in-depth, enabling you to assess their suitability for the position. Ensure that the questions are relevant, insightful, and appropriate, and avoid asking any questions that may be considered inappropriate. Your goal is to gain a comprehensive understanding of the candidate's capabilities and determine their alignment with the job requirements. Generate a set of follow-up interview questions that will help you make an informed hiring decision."""}
    ]
    messages.append({"role": "user", "content": text})
    return messages 

def get_experience_level_direct(resume_text):
    prompt = f"""As an AI Hiring Specialist, your task is to analyize the experience level of the client taking into account the Summary, previous experience, education from the given resume text. Consider Previous Experience timeline & education timeline as an important factor. The output should be mostly like: Fresher, Intermediate, __ years experience, etc. 
    
    Resume Text: {resume_text}

    Response Generation Guidelines: 
    Generate response as valid JSON with single key "experience_level" and value as the experience level identified from the resume text.
    """
    try:
        messages = [ {"role": "system", "content": prompt}]
        messages.append({"role": "user", "content": "\Resume Text:\n"})
        return messages
    except Exception as e:
        print("Exception: ", e)
        return None
    
def get_role(resume_text):
    prompt = f"""As an AI Hiring Specialist, your task is to analyize the role the client is going to apply for taking into account the Summary, previous experience, skillset, education from the given resume text. Consider Previous Experience roles & the new role the person seeking if any mentioned in the summary as in greater priority for identifying the role. 
    
    Resume Text: {resume_text}

    Response Generation Guidelines: 
    Generate response as valid JSON with single key "role" and value as the role identified from the resume text.
    """
    try:
        messages = [ {"role": "system", "content": prompt}]
        messages.append({"role": "user", "content": "\Resume Text:\n"})
        return messages
    except Exception as e:
        print("Exception: ", e)
        return None