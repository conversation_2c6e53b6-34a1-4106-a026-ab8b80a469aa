"""
This module provides functions to manage job statuses in a database. It includes functionalities to check if all tasks are complete, 
insert new records, update statuses, and delete status records associated with specific session IDs and request types.
"""

from api.common.api_logger import api_logger as logger
from api.database.connection_db import get_connection

def are_all_tasks_complete(session_id: str, connection) -> bool:
    """
    Checks if all tasks associated with a given session ID are complete.

    Parameters:
    - session_id (str): The session ID to check.
    - connection: The database connection object.

    Returns:
    - bool: True if all tasks are complete or if the session ID is not found, False otherwise.
    """
    try:
        logger.info(f"Checking if all tasks are complete for session_id: {session_id}")
        with connection.cursor() as cursor:
            # Fetch all rows for the given session_id
            cursor.execute("SELECT status, request_type FROM jobs WHERE session_id = %s", (session_id,))
            rows = cursor.fetchall()

            # Check if the session ID is not found in the database
            if not rows:
                return True  # If session ID is not found, consider tasks as complete

            # Check if all rows have the status set to "complete" or "failed"
            logger.info(f"Job status for session {session_id}: {rows}")
            return all(row['status'] == 'complete' or row['status'] == 'failed' for row in rows)

    except Exception as e:
        logger.error(f"Error checking job status: {e} for session {session_id}")
        return True

def insert_record(session_id, request_type, gpt_response, response_time, model, total_tokens, number_of_questions=None, role=None, experience_level=None, jd_text=None, resume_text=None, connection=None):
    """
    Inserts a new record into the requests table.

    Parameters:
    - session_id (str): The session ID.
    - request_type (str): The type of request.
    - gpt_response (str or dict): The response from GPT.
    - response_time (int): The time taken to generate the response.
    - model (str): The model used for generating the response.
    - total_tokens (int): The total number of tokens used.
    - number_of_questions (Optional[int]): The number of questions generated.
    - role (Optional[str]): The role associated with the request.
    - experience_level (Optional[str]): The experience level associated with the request.
    - jd_text (Optional[str]): The job description text.
    - resume_text (Optional[str]): The resume text.
    - connection: The database connection object.
    """
    try:
        logger.info(f"Inserting record for session_id: {session_id}, request_type: {request_type}")
        
        # Convert dictionary to JSON string if needed
        if isinstance(gpt_response, dict):
            import json
            gpt_response = json.dumps(gpt_response)
        
        # Establish a connection using get_connection function
        with connection.cursor() as cursor:
            # SQL query to insert a new record
            if experience_level and role:
                sql = "INSERT INTO requests (session_id, request_type, gpt_response, response_time, number_of_questions, model, total_tokens, role, experience_level) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (session_id, request_type, gpt_response, response_time, number_of_questions, model, total_tokens, role, experience_level))
            elif role and not experience_level:
                sql = "INSERT INTO requests (session_id, request_type, gpt_response, response_time, number_of_questions, model, total_tokens, role) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (session_id, request_type, gpt_response, response_time, number_of_questions, model, total_tokens, role))
            elif jd_text and not role:
                sql = "INSERT INTO requests (session_id, request_type, gpt_response, response_time, model, total_tokens, job_description) VALUES (%s, %s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (session_id, request_type, gpt_response, response_time, model, total_tokens,jd_text))
            elif resume_text and not role:
                sql = "INSERT INTO requests (session_id, request_type, gpt_response, response_time, model, total_tokens, resume_text) VALUES (%s, %s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (session_id, request_type, gpt_response, response_time, model, total_tokens,resume_text))
            else:
                sql = "INSERT INTO requests (session_id, request_type, gpt_response, response_time, model, total_tokens) VALUES (%s, %s, %s, %s, %s, %s)"
                cursor.execute(sql, (session_id, request_type, gpt_response, response_time, model, total_tokens))
        
        # Commit the transaction
        connection.commit()
    except Exception as e:
        logger.error(f"An error occurred while inserting record: {e}")

def insert_status(session_id, request_type, status, connection):
    """
    Inserts a new status record into the jobs table.

    Parameters:
    - session_id (str): The session ID.
    - request_type (str): The type of request.
    - status (str): The status to insert.
    - connection: The database connection object.
    """
    try:
        logger.info(f"Inserting status for session_id: {session_id}, request_type: {request_type}, status: {status}")
        with connection.cursor() as cursor:
            # SQL statement to insert a new record
            sql = "INSERT INTO jobs (session_id, request_type, status) VALUES (%s, %s, %s)"
            
            # Execute the SQL statement
            cursor.execute(sql, (session_id, request_type, status))

        # Commit the transaction to persist the record in the database
        connection.commit()

    except Exception as e:
        logger.error("Error inserting record:", str(e))
        raise e

def update_status(session_id, request_type, status, connection):
    """
    Updates the status of a job in the jobs table.

    Parameters:
    - session_id (str): The session ID.
    - request_type (str): The type of request.
    - status (str): The new status to set.
    - connection: The database connection object.
    """
    try:
        logger.info(f"Updating status for session_id: {session_id}, request_type: {request_type}, status: {status}")
        with connection.cursor() as cursor:
            # SQL statement to insert or update on duplicate key
            sql = """
            INSERT INTO jobs (session_id, request_type, status)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE status = VALUES(status);
            """
            # Execute the SQL statement
            cursor.execute(sql, (session_id, request_type, status))

            # Commit the transaction to persist the update or insert in the database
        connection.commit()
        logger.info("Status updated successfully.")
            
    except Exception as e:
        logger.error("Error updating or inserting status:", str(e))
        raise e


def delete_status(session_id, request_type):
    """
    Deletes a status record from the jobs table.

    Parameters:
    - session_id (str): The session ID.
    - request_type (str): The type of request.
    """
    try:
        logger.info(f"Deleting status for session_id: {session_id}, request_type: {request_type}")
        # Create a connection
        connection = get_connection()

        # Create a cursor
        with connection.cursor() as cursor:
            # SQL statement to update the status
            cursor.execute("DELETE FROM requests WHERE session_id = %s and request_type = %s", (session_id, request_type))

        connection.commit()
        logger.info("Complete status...")

    except Exception as e:
        logger.error("Error deleting status:", str(e))
        raise e

    finally:
        # Close the connection
        connection.close()