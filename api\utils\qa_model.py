"""
This module handles interactions with the OpenAI API to generate responses
based on provided messages and models.
"""

from langchain_openai import ChatOpenAI
from dotenv import load_dotenv
from api.common.pydantic_models import QuestionList, Feedback
import os
from api.common.api_logger import api_logger as logger
from langchain_community.callbacks.manager import get_openai_callback
from langsmith import traceable
import time

load_dotenv()

# model = "gpt-4"
# # model = "gpt-3.5-turbo"
@traceable(name="Generate Response")
def generate_response(messages, model, session_id=None):
    """
    Generates a response from the OpenAI API based on the provided messages
    and model. Logs the process and handles errors.
    
    Args:
        messages: The messages to send to the API
        model: The model to use for generation
        session_id: Optional session ID to include in LangSmith metadata
    """
    logger.info("Generating response with model: %s", model)  # Log the model being used
    try:
        # Initialize the ChatOpenAI model
        client = ChatOpenAI(
            api_key=os.getenv('openai_api_key'),
            timeout=120.0,
            model=model
        )
        
        # Configure the model for structured output
        structured_llm = client.with_structured_output(QuestionList)
        
        
        # Use the callback handler to track token usage
        with get_openai_callback() as cb:
            # Pass the configuration as the second argument
            structured_response = structured_llm.invoke(messages, {"metadata": {"session_id": session_id}})
            token_usage = cb.total_tokens
        
        # Convert Pydantic model to dictionary
        response = (structured_response.model_dump(), token_usage)
        if response and response[0]:
            logger.info("Response received successfully.")  # Log successful response
            return response
        else:
            logger.error("Received an empty response")  # Log error for empty response
            raise ValueError("Received an empty response")
    except Exception as e:
        logger.error("Error occurred: %s", e)  # Log the error
        try:
            logger.info("Retrying Again")
            # Initialize the ChatOpenAI model
            client = ChatOpenAI(
                api_key=os.getenv('openai_api_key'),
                timeout=120.0,
                model=model
            )
            
            # Configure the model for structured output
            structured_llm = client.with_structured_output(QuestionList)
            
            # Use the callback handler to track token usage
            with get_openai_callback() as cb:
                structured_response = structured_llm.invoke(messages, {"metadata": {"session_id": session_id}})
                token_usage = cb.total_tokens
            
            # Convert Pydantic model to dictionary in retry as well
            response = (structured_response.model_dump(), token_usage)
            if response and response[0]:
                logger.info("Response received successfully.")  # Log successful response
                return response
            else:
                logger.error("Received an empty response")  # Log error for empty response
                raise ValueError("Received an empty response")
        except Exception as retry_error:
            logger.error("Retry failed: %s", retry_error)  # Log retry failure
            return None

@traceable(name="Generate Feedback Response")
def generate_feedback_response(messages, model):
    """
    Generates a response from the OpenAI API based on the provided messages
    and model. Logs the process and handles errors.
    
    Args:
        messages: The messages to send to the API
        model: The model to use for generation
    """
    logger.info("Generating response with model: %s", model)  # Log the model being used
    try:
        # Initialize the ChatOpenAI model
        client = ChatOpenAI(
            api_key=os.getenv('openai_api_key'),
            timeout=120.0,
            model=model
        )
        
        # Configure the model for structured output
        structured_llm = client.with_structured_output(Feedback)
        
        
        # Use the callback handler to track token usage
        with get_openai_callback() as cb:
            # Pass the configuration as the second argument
            structured_response = structured_llm.invoke(messages)
            token_usage = cb.total_tokens
        
        # Convert Pydantic model to dictionary
        response = (structured_response.model_dump(), token_usage)
        if response and response[0]:
            logger.info("Response received successfully.")  # Log successful response
            return response
        else:
            logger.error("Received an empty response")  # Log error for empty response
            raise ValueError("Received an empty response")
    except Exception as e:
        logger.error("Error occurred: %s", e)  # Log the error
        try:
            logger.info("Retrying Again")
            # Initialize the ChatOpenAI model
            client = ChatOpenAI(
                api_key=os.getenv('openai_api_key'),
                timeout=120.0,
                model=model
            )
            
            # Configure the model for structured output
            structured_llm = client.with_structured_output(Feedback)
            
            # Use the callback handler to track token usage
            with get_openai_callback() as cb:
                structured_response = structured_llm.invoke(messages)
                token_usage = cb.total_tokens
            
            # Convert Pydantic model to dictionary in retry as well
            response = (structured_response.model_dump(), token_usage)
            if response and response[0]:
                logger.info("Response received successfully.")  # Log successful response
                return response
            else:
                logger.error("Received an empty response")  # Log error for empty response
                raise ValueError("Received an empty response")
        except Exception as retry_error:
            logger.error("Retry failed: %s", retry_error)  # Log retry failure
            return None
