import pymysql
import csv
from datetime import datetime
import os
from dotenv import load_dotenv
load_dotenv()

# Database connection details from environment variables

host=os.getenv("mysql_host")
database=os.getenv("mysql_database")
user=os.getenv("mysql_user")
password=os.getenv("mysql_password")


def get_connection():
    # Replace these values with your database connection details
    connection = pymysql.connect(host=host,
                                 user=user,
                                 password=password,
                                 database=database,
                                 cursorclass=pymysql.cursors.DictCursor)
    return connection

# Backup table data to CSV
def backup_table_to_csv(table_name):
    connection = get_connection()
    try:
        with connection.cursor() as cursor:
            query = f"SELECT * FROM {table_name}"
            cursor.execute(query)
            result = cursor.fetchall()

            # Define CSV file name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"backup/{table_name}_backup_{timestamp}.csv"

            # Write data to CSV file
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=result[0].keys())
                writer.writeheader()
                for row in result:
                    writer.writerow(row)

            print(f"Backup for table {table_name} completed as {filename}")
    except Exception as e:
        print(f"Error backing up table {table_name}: {e}")
    finally:
        connection.close()

# Cleanup table contents
def cleanup_table(table_name):
    connection = get_connection()
    try:
        with connection.cursor() as cursor:
            query = f"DELETE FROM {table_name}"
            cursor.execute(query)
            connection.commit()
            print(f"All records deleted from {table_name}")
    except Exception as e:
        print(f"Error cleaning up table {table_name}: {e}")
    finally:
        connection.close()

# Define the tables to backup and cleanup
tables = ['requests', 'jobs']

# Run backup and cleanup for each table
for table in tables:
    backup_table_to_csv(table)
    cleanup_table(table)
