"""
This module handles question generation and related tasks.
It provides endpoints for uploading job titles, JD, and resumes.
"""

from fastapi import APIRouter, BackgroundTasks, Depends, Form, HTTPException, Query, UploadFile
from typing import Optional
import os
from api.common.dependencies import get_api_key
from api.database.connection_db import get_connection
from api.utils.function_utils import get_file_text_fastapi
from api.utils.processing_functions import generate_response_background, process_resume, process_upload_jd
from api.common.pydantic_models import GPTModels
from api.common.api_logger import api_logger as logger

router = APIRouter()


@router.post("/upload_job_title_experience_level/")
def upload_job_title(
    background_tasks: BackgroundTasks, 
    session_id: str, 
    job_title: str, 
    number_of_questions: int, 
    model: GPTModels = Query(...), 
    experience_level: Optional[str] = Query(None), 
    is_ca: bool = Query(False),  # Optional boolean parameter with default value False
    token: bool = Depends(get_api_key)
):
    """
    Uploads job title, experience level, number of questions, and processess in the background.
    """
    #print all the parameters
    logger.info(f"Request received for Job Title with Session ID: {session_id} Model: {model.value} and is_ca: {is_ca} and Experience Level: {experience_level} and Number of Questions: {number_of_questions}")
                     
    try:
        connection = get_connection(is_ca)
        model = model.value
        if model=="gpt-3.5-turbo" or model == "gpt-3.5-turbo-1106":
            model="gpt-4o-mini"
        elif model == "gpt-4-turbo-preview" or model =="gpt-4-1106-preview":
            model = "gpt-4o"
        
        # Add background task to execute process_background_task
        background_tasks.add_task(
            generate_response_background,
            session_id=session_id,
            job_title=job_title,
            experience_level=experience_level,
            request_type = "job_title",
            model=model,
            number_of_questions=number_of_questions,
            connection=connection
        )

        # Send response to the client
        result_dict = {"status": True, "message": "Request received, processing in the background"}
        logger.info(f"Job Title Request with Session ID: {session_id} Successfully received and added to background task")
        return result_dict
        
        
    except Exception as e:
        logger.error(f"Exception: {e} Occurred while processing job title request")
        return {"status": False, "error": str(e)}


@router.post("/upload_jd/")
async def upload_jd(
    background_tasks: BackgroundTasks,
    session_id: str,
    jd_text: str = Form(...),
    model: GPTModels = Query(...),
    is_ca: bool = Query(False),
    token: bool = Depends(get_api_key)  # Inject BackgroundTasks as a dependency
):
    """
    Uploads a job description (JD) for processing in the background.
    """
    logger.info(f"Request received for JD with Session ID: {session_id} Model: {model.value} and is_ca: {is_ca}")
    try:
        connection = get_connection(is_ca)
        model = model.value
        if model=="gpt-3.5-turbo" or model == "gpt-3.5-turbo-1106":
            model="gpt-4o-mini"
        elif model == "gpt-4-turbo-preview" or model =="gpt-4-1106-preview":
            model = "gpt-4o"
                
        jd_file_name = None
        # Determine the source of the JD text
        # if jd_text:
        text = jd_text
        # elif jd:
        #     file_extension = jd.filename.split('.')[-1].lower()

        #     # Check if the file extension is in the allowed list
        #     if file_extension not in ['docx', 'pdf']:
        #         raise HTTPException(status_code=415, detail="File type not supported")
        #     jd_file_name = f"uploaded_{jd.filename}"
        #     jd_file_name = os.path.join("temp", jd_file_name)
        #     with open(jd_file_name, "wb") as f2:
        #         f2.write(jd.file.read())
        #     text = get_file_text_fastapi(jd_file_name)
        # else:
        #     raise HTTPException(status_code=400, detail="No file uploaded")

        if len(text) < 2:
            raise HTTPException(status_code=422, detail="Insufficient JD text provided")

        # Add background task to execute process_upload_jd
        background_tasks.add_task(process_upload_jd, session_id, text, model, connection)

        # Add cleanup task to delete the JD file after processing
        if jd_file_name:
            background_tasks.add_task(os.remove, jd_file_name)

        # Send response to the client
        result_dict = {"status": True, "message": "Request received, processing in the background"}
        logger.info(f"JD Request with Session ID: {session_id} Successfully received and added to background task")
        return result_dict

    except Exception as e:
        logger.error(f"Exception: {e} Occurred while processing JD request")
        return {"status": False, "error": str(e)}


@router.post("/upload_resume/")
async def upload_resume(
    background_tasks: BackgroundTasks,
    session_id: str,
    resume: UploadFile,
    model: GPTModels = Query(...),
    is_ca: bool = Query(False),
    token: bool = Depends(get_api_key),
):
    """
    Uploads a resume for processing in the background.
    """
    logger.info(f"Request received for Resume with Session ID: {session_id} Model: {model.value} and is_ca: {is_ca}")

    try:
        connection = get_connection(is_ca)
        model = model.value
        if model=="gpt-3.5-turbo" or model == "gpt-3.5-turbo-1106":
            model="gpt-4o-mini"
        elif model == "gpt-4-turbo-preview" or model =="gpt-4-1106-preview":
            model = "gpt-4o"
            
        if resume.filename.split('.')[-1].lower() not in ['docx', 'pdf']:
            raise HTTPException(status_code=415, detail="File type not supported")

        resume_file_name = f"uploaded_{resume.filename}"
        resume_file_name = os.path.join("temp", resume_file_name)

        with open(resume_file_name, "wb") as f1:
            f1.write(resume.file.read())

        # Add the background task
        background_tasks.add_task(process_resume, session_id, resume_file_name, model, connection)
        background_tasks.add_task(os.remove, resume_file_name)
        result_dict = {"status": True, "message": "Request received, processing in the background"}
        logger.info(f"Resume Request with Session ID: {session_id} Successfully received and added to background task")
        return result_dict
        
    except Exception as e:
        logger.error(f"Exception: {e} Occurred while processing resume request")
        return {"status": False, "error": str(e)}
