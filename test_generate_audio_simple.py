"""
Simple test for the Generate Audio endpoint.
Tests the core functionality: accept questions, generate audio, return job ID.
"""

import requests
import time
import os
from dotenv import load_dotenv

load_dotenv()

# API Configuration
BASE_URL = "http://localhost:8001"
API_KEY = os.getenv("API_KEY_NAME", "myinterviewpractice")
API_SECRET = os.getenv("API_SECRET_NAME", "a0a408ef-e2b3-4680-bd58-35dbf66e1a7f")

headers = {
    "Content-Type": "application/json",
    "API_KEY": API_KEY,
    "API_SECRET": API_SECRET
}

def test_generate_audio_endpoint():
    """Test the core Generate Audio endpoint functionality."""
    print("🎵 Testing Generate Audio Endpoint")
    print("=" * 50)
    
    # Sample questions for testing
    test_questions = [
        "Tell me about yourself and your background.",
        "What are your greatest strengths and how do they apply to this role?",
        "Describe a challenging project you worked on and how you overcame obstacles."
    ]
    
    # Request payload
    request_data = {
        "questions": test_questions,
        "voice_id": "21m00Tcm4TlvDq8ikWAM",  # Rachel - Asian Women
        "model": "eleven_flash_v2_5"
    }
    
    print(f"📝 Sending {len(test_questions)} questions for audio generation...")
    print("Questions:")
    for i, q in enumerate(test_questions, 1):
        print(f"  {i}. {q}")
    
    print(f"\n🎤 Using voice: Rachel (Asian Women)")
    print(f"🤖 Using model: eleven_flash_v2_5")
    
    # Make the request
    try:
        response = requests.post(
            f"{BASE_URL}/audio/generate-audio",
            json=request_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            job_id = data["job_id"]
            
            print(f"\n✅ SUCCESS! Audio generation job created")
            print(f"🆔 Job ID: {job_id}")
            print(f"📊 Status: {data['status']}")
            print(f"📈 Total questions: {data['total_questions']}")
            print(f"🎵 Voice used: {data['voice_used']}")
            print(f"⏰ Created at: {data['created_at']}")
            
            return job_id
            
        else:
            print(f"❌ FAILED! Status code: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Cannot connect to the server")
        print("Make sure the FastAPI server is running:")
        print("  uvicorn api.main:app --reload")
        return None
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT ERROR: Request took too long")
        return None
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {str(e)}")
        return None

def monitor_job_progress(job_id, max_wait_time=120):
    """Monitor the job progress until completion."""
    print(f"\n📊 Monitoring job progress: {job_id}")
    print("-" * 50)
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{BASE_URL}/audio/job-status/{job_id}")
            
            if response.status_code == 200:
                data = response.json()
                status = data["status"]
                progress = data["progress"]
                completed_files = len(data["audio_files"])
                
                print(f"⏳ Status: {status} | Progress: {progress}% | Files: {completed_files}")
                
                if status == "completed":
                    print("🎉 Job completed successfully!")
                    return data
                elif status == "failed":
                    error_msg = data.get("error_message", "Unknown error")
                    print(f"❌ Job failed: {error_msg}")
                    return None
                elif status == "partial":
                    print(f"⚠️  Job partially completed")
                    return data
                
                # Wait before next check
                time.sleep(3)
            else:
                print(f"❌ Error checking status: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error monitoring job: {str(e)}")
            return None
    
    print("⏰ Timeout waiting for job completion")
    return None

def display_results(job_data):
    """Display the final results."""
    if not job_data:
        return
    
    print(f"\n🎯 FINAL RESULTS")
    print("=" * 50)
    
    audio_files = job_data.get("audio_files", [])
    print(f"✅ Generated {len(audio_files)} audio files")
    
    total_duration = 0
    for i, audio_file in enumerate(audio_files, 1):
        question_text = audio_file["question_text"]
        duration = audio_file.get("duration_seconds", 0)
        file_url = audio_file["file_url"]
        
        total_duration += duration or 0
        
        print(f"\n🎵 Audio File {i}:")
        print(f"   Question: {question_text[:60]}...")
        print(f"   Duration: {duration:.1f} seconds")
        print(f"   Download URL: {file_url}")
    
    print(f"\n📊 Summary:")
    print(f"   Total files: {len(audio_files)}")
    print(f"   Total duration: {total_duration:.1f} seconds")
    print(f"   Average duration: {total_duration/len(audio_files):.1f} seconds per question")

def run_complete_test():
    """Run the complete test workflow."""
    print("🚀 GENERATE AUDIO ENDPOINT TEST")
    print("=" * 60)
    
    # Step 1: Generate audio
    job_id = test_generate_audio_endpoint()
    if not job_id:
        print("\n❌ Test failed at audio generation step")
        return
    
    # Step 2: Monitor progress
    job_data = monitor_job_progress(job_id)
    if not job_data:
        print("\n❌ Test failed at monitoring step")
        return
    
    # Step 3: Display results
    display_results(job_data)
    
    print(f"\n🎉 TEST COMPLETED SUCCESSFULLY!")
    print(f"🆔 Job ID for reference: {job_id}")
    print("\n💡 Next steps:")
    print("   1. Use the download URLs to get the audio files")
    print("   2. Test the individual file download endpoints")
    print("   3. Implement client-side audio playback")

if __name__ == "__main__":
    run_complete_test()
