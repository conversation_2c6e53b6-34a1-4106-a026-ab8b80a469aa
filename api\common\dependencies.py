"""
This module defines dependencies for FastAPI routes, including API key
authentication using headers and query parameters.
"""

from fastapi.security import <PERSON><PERSON>eyHeader, APIKeyQuery
from fastapi import Depends, HTTPException, Header, status
import os
from dotenv import load_dotenv
load_dotenv()

API_KEY_NAME = os.getenv("API_KEY_NAME")
API_SECRET_NAME = os.getenv("API_SECRET_NAME")

api_key_header = APIKeyHeader(name="API_KEY", auto_error=False)
api_key_query = APIKeyQuery(name="API_SECRET", auto_error=False)

async def get_api_key(
    api_key: str = Header(...), api_secret: str = Header(...)
):

    if api_key == API_KEY_NAME and api_secret == API_SECRET_NAME:
        return True
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key"
        )
