{"cells": [{"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "# Assuming your FastAPI app is running locally on port 8000\n", "# base_url = \"http://**********:8001\"\n", "base_url = \"https://ai.myinterviewpractice.com\"\n", "# base_url = \"http://localhost:8000\"\n", "\n", "key = \"myinterviewpractice\"\n", "secret = \"a0a408ef-e2b3-4680-bd58-35dbf66e1a7f\"\n", "headers = {\n", "    'accept': 'application/json',\n", "    'api-key': key,\n", "    'api-secret': secret\n", "}\n", "\n", "def get_prompt(request_type):\n", "    response = requests.get(f\"{base_url}/get_prompt/\", params={\"request_type\": request_type}, headers=headers)\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    else:\n", "        print(\"Failed to fetch prompt. Status Code:\", response.status_code)\n", "\n", "def update_prompt(request_type, prompt):\n", "    response = requests.post(f\"{base_url}/update_prompt/\", data={\"request_type\": request_type, \"prompt\": prompt}, headers=headers)\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    else:\n", "        print(\"Failed to update prompt. Status Code:\", response.status_code)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def update_prompt(request_type, prompt):\n", "    response = requests.post(\n", "        f\"{base_url}/update_prompt/\",\n", "        files={\"request_type\": (None, request_type), \"prompt\": (None, prompt)}\n", "    )\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    else:\n", "        print(\"Failed to update prompt. Status Code:\", response.status_code)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["As An AI Hiring specialist, your task is to analyze the provided job role{experience_note} and generate a list of relevant interview questions. Take into account the following notes to create insightful inquiries:  Notes for you to consider while creating questions: \n", "1. Prepare a list of skills required for the role{experience_note}: {role} \n", "2. Generate a list of questions that will help you assess the candidates proficiency in each of these skills. \n", "3. Ensure that the questions are relevant, insightful, and appropriate, and avoid asking any questions that may be considered inappropriate. \n", "4. Your goal is to gain a comprehensive understanding of the candidates capabilities and determine their alignment with the job requirements. \n", "5. Generate a set of follow-up interview questions that will help you make an informed hiring decision. You can ask questions such as: 1. What is your experience with [specific requirement]? 2. How has your previous experience prepared you for this role{experience_note}? 3. How will your expertise in [specific skill] contribute to your success in this role{experience_note}? 4. How did you handle [a specific task related to {role}] ? 5. Could you describe your typical approach to utilizing this skill? \n", "6. Describe a situation where you had to prioritize competing tasks related to {role}, and how did you manage your time and resources effectively? \n", "7. How do you approach training or mentoring team members who may not be as proficient in these skills?\n"]}], "source": ["# Example usage\n", "request_type = \"job_title\"  # Choose from 'job_title', 'jd', 'resume', 'interview_experience'\n", "response = get_prompt(request_type)\n", "print(response['prompt'])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"status\":true,\"message\":\"Prompt updated successfully\"}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100   347    0     0  100   347      0    284  0:00:01  0:00:01 --:--:--   284\n", " 86   402    0     0  100   347      0    215  0:00:01  0:00:01 --:--:--   215\n", "100   402  100    55  100   347     29    186  0:00:01  0:00:01 --:--:--   215\n"]}], "source": ["!curl -X POST \"http://**********:8001/update_prompt/\" -H \"Content-Type: multipart/form-data\" -F \"request_type=job_title\" -F \"prompt= Prepare a good list of skills required for the role {experience_note} : {role} \""]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["new_prompt = \"\"\"As An AI Hiring specialist, your task is to analyze the provided job role{experience_note} and generate a list of relevant interview questions. Take into account the following notes to create insightful inquiries:  Notes for you to consider while creating questions: \n", "1. Prepare a list of skills required for the role{experience_note}: {role} \n", "2. Generate a list of questions that will help you assess the candidates proficiency in each of these skills. \n", "3. Ensure that the questions are relevant, insightful, and appropriate, and avoid asking any questions that may be considered inappropriate. \n", "4. Your goal is to gain a comprehensive understanding of the candidates capabilities and determine their alignment with the job requirements. \n", "5. Generate a set of follow-up interview questions that will help you make an informed hiring decision. You can ask questions such as: 1. What is your experience with [specific requirement]? 2. How has your previous experience prepared you for this role{experience_note}? 3. How will your expertise in [specific skill] contribute to your success in this role{experience_note}? 4. How did you handle [a specific task related to {role}] ? 5. Could you describe your typical approach to utilizing this skill? \n", "6. Describe a situation where you had to prioritize competing tasks related to {role}, and how did you manage your time and resources effectively? \n", "7. How do you approach training or mentoring team members who may not be as proficient in these skills?\"\"\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'status': True, 'message': 'Prompt updated successfully'}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "update_prompt(request_type, new_prompt)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["session_id = \"pradip_test_26\"\n"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"status\":true,\"message\":\"Request received, processing in the background\"}\n"]}], "source": ["url = base_url + '/upload_job_title_experience_level/'\n", "\n", "params = {\n", "    'session_id': session_id,\n", "    'job_title': 'Data Scientist',\n", "    'number_of_questions': '11',\n", "    'model': 'gpt-3.5-turbo',\n", "    'experience_level': 'Internship'\n", "}\n", "\n", "\n", "response = requests.post(url, headers=headers, params=params)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"status\":true,\"message\":\"Request received, processing in the background\"}\n"]}], "source": ["import requests\n", "\n", "url = base_url + '/upload_jd/'\n", "params = {\n", "    'session_id': session_id,\n", "    'model': 'gpt-3.5-turbo'\n", "}\n", "\n", "files = {\n", "    'jd_text': (None, ''),\n", "    'jd': ('jd.docx', open('jd.docx', 'rb'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')\n", "}\n", "\n", "response = requests.post(url, headers=headers, params=params, files=files)\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"status\":true,\"message\":\"Request received, processing in the background\"}\n"]}], "source": ["import requests\n", "\n", "url = base_url + '/upload_resume/'\n", "params = {\n", "    'session_id': session_id,\n", "    'model': 'gpt-3.5-turbo'\n", "}\n", "\n", "files = {\n", "    'resume': ('resume - Copy.docx', open('resume - Copy.docx', 'rb'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')\n", "}\n", "\n", "response = requests.post(url, headers=headers, params=params, files=files)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"session_id\":\"pradip_test_26\",\"questions\":[\"Can you tell me about yourself and your interest in data science?\",\"What experience do you have with statistical analysis and data modeling?\",\"Can you describe a project where you utilized machine learning algorithms?\",\"How comfortable are you with programming languages such as Python, R, or Java?\",\"How do you approach data visualization to communicate findings effectively?\",\"Can you explain your process for cleaning and preprocessing data before analysis?\",\"How familiar are you with tools like SQL, Hadoop, or Spark for data manipulation and analysis?\",\"Describe a situation where you had to prioritize competing tasks related to data analysis. How did you manage your time and resources effectively?\",\"How do you stay updated with the latest trends and technologies in the field of data science?\",\"Can you provide an example of a successful data-driven decision you made in a project?\",\"How do you approach training or mentoring team members who may not be as proficient in data science skills?\",\"Do you have any questions for us?\"],\"status\":true}\n"]}], "source": ["url = base_url + '/get_interview_experience/'\n", "\n", "params = {\n", "    'session_id': session_id,\n", "    'model': 'gpt-3.5-turbo',\n", "}\n", "response = requests.post(url, headers=headers, params=params)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 2}