"""
Audio generation router for TTS (Text-to-Speech) functionality.
Handles audio generation requests, job tracking, and file downloads.
"""

import asyncio
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import FileResponse
from typing import List
from api.common.dependencies import get_api_key
from api.common.api_logger import api_logger as logger
from api.common.pydantic_models import (
    AudioGenerationRequest,
    AudioGenerationResponse,
    AudioJobStatus,
    VoiceMapping,
    QuestionAudioRequest,
    QuestionAudioResponse
)
from api.utils.tts_service import tts_service
from api.database.audio_jobs_db import audio_jobs_db
from datetime import datetime

router = APIRouter(
    prefix="/audio",
    tags=["Audio Generation"]
)

async def process_audio_generation_background(
    job_id: str,
    questions: List[str],
    voice_id: str,
    model: str
):
    """
    Background task to process audio generation for multiple questions.

    Args:
        job_id: Unique job identifier
        questions: List of questions to convert to audio
        voice_id: ElevenLabs voice ID
        model: TTS model to use
    """
    try:
        logger.info(f"Starting background audio generation for job: {job_id}")

        # Update job status to processing
        audio_jobs_db.update_job_status(job_id, "processing", 0)

        # Generate audio files
        audio_files = await tts_service.generate_multiple_audio_files(
            questions=questions,
            voice_id=voice_id,
            job_id=job_id,
            model=model
        )

        # Add each audio file to the job
        for audio_file in audio_files:
            audio_jobs_db.add_audio_file(job_id, audio_file)

        # Update final status
        if len(audio_files) == len(questions):
            audio_jobs_db.update_job_status(job_id, "completed", 100)
            logger.info(f"Audio generation completed successfully for job: {job_id}")
        else:
            audio_jobs_db.update_job_status(
                job_id,
                "partial",
                int((len(audio_files) / len(questions)) * 100),
                f"Generated {len(audio_files)}/{len(questions)} audio files"
            )
            logger.warning(f"Partial completion for job {job_id}: {len(audio_files)}/{len(questions)} files")

    except Exception as e:
        logger.error(f"Error in background audio generation for job {job_id}: {str(e)}")
        audio_jobs_db.update_job_status(job_id, "failed", error_message=str(e))

@router.post("/generate-audio", response_model=AudioGenerationResponse)
async def generate_audio(
    request: AudioGenerationRequest,
    background_tasks: BackgroundTasks,
    token: bool = Depends(get_api_key)
) -> AudioGenerationResponse:
    """
    Generate audio files for a list of questions using ElevenLabs TTS.

    This endpoint accepts a list of questions and generates audio files for each one.
    The generation happens in the background, and a job ID is returned for tracking progress.

    Args:
        request: AudioGenerationRequest containing questions, voice_id, and model
        background_tasks: FastAPI background tasks
        token: API authentication token

    Returns:
        AudioGenerationResponse with job details and initial status
    """
    try:
        logger.info(f"Audio generation request received for {len(request.questions)} questions")

        # Validate input
        if not request.questions:
            raise HTTPException(status_code=400, detail="Questions list cannot be empty")

        if len(request.questions) > 50:  # Reasonable limit
            raise HTTPException(status_code=400, detail="Maximum 50 questions allowed per request")

        # Generate unique job ID
        job_id = tts_service.generate_job_id()

        # Validate voice_id
        valid_voices = [voice.value for voice in VoiceMapping]
        if request.voice_id not in valid_voices:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid voice_id. Must be one of: {valid_voices}"
            )

        # Create job in database
        success = audio_jobs_db.create_job(
            job_id=job_id,
            questions=request.questions,
            voice_id=request.voice_id,
            model=request.model
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to create audio generation job")

        # Start background processing
        background_tasks.add_task(
            process_audio_generation_background,
            job_id=job_id,
            questions=request.questions,
            voice_id=request.voice_id,
            model=request.model
        )

        # Return initial response
        response = AudioGenerationResponse(
            job_id=job_id,
            status="pending",
            total_questions=len(request.questions),
            audio_files=[],
            created_at=datetime.now().isoformat(),
            voice_used=request.voice_id.value
        )

        logger.info(f"Audio generation job created: {job_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating audio generation job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/job-status/{job_id}", response_model=AudioJobStatus)
async def get_job_status(job_id: str) -> AudioJobStatus:
    """
    Get the current status of an audio generation job.

    Args:
        job_id: The unique job identifier

    Returns:
        AudioJobStatus with current progress and completed files
    """
    try:
        job_status = audio_jobs_db.get_job_status(job_id)

        if not job_status:
            raise HTTPException(status_code=404, detail="Job not found")

        return job_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/download/{job_id}/{filename}")
async def download_audio_file(job_id: str, filename: str):
    """
    Download a specific audio file from a completed job.

    Args:
        job_id: The unique job identifier
        filename: The audio file name

    Returns:
        FileResponse with the audio file
    """
    try:
        # Get file path
        file_path = tts_service.get_file_path(job_id, filename)

        if not file_path or not file_path.exists():
            raise HTTPException(status_code=404, detail="Audio file not found")

        # Return file response
        return FileResponse(
            path=str(file_path),
            media_type="audio/mpeg",
            filename=filename,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {filename} from job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/voices")
async def get_available_voices():
    """
    Get list of available voices with their descriptions.

    Returns:
        Dictionary mapping voice IDs to descriptions
    """
    try:
        voices = tts_service.get_available_voices()
        return {
            "voices": voices,
            "default_model": "eleven_flash_v2_5",
            "supported_models": [
                "eleven_flash_v2_5",
                "eleven_multilingual_v2",
                "eleven_turbo_v2_5"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting available voices: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/job/{job_id}")
async def delete_job(job_id: str, token: bool = Depends(get_api_key)):
    """
    Delete an audio generation job and clean up associated files.

    Args:
        job_id: The unique job identifier
        token: API authentication token

    Returns:
        Success message
    """
    try:
        # Check if job exists
        job_status = audio_jobs_db.get_job_status(job_id)
        if not job_status:
            raise HTTPException(status_code=404, detail="Job not found")

        # Clean up files
        tts_service.cleanup_job_files(job_id)

        # Delete job from database
        audio_jobs_db.delete_job(job_id)

        logger.info(f"Deleted audio job: {job_id}")
        return {"message": f"Job {job_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
