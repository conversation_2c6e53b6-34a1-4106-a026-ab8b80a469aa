2025-04-18 01:19:12,337 - api_logger - INFO - Request received for Job Title with Session ID: new-1 Model: gpt-4o-mini and is_ca: False and Experience Level: mid and Number of Questions: 7
2025-04-18 01:19:14,071 - api_logger - INFO - Job Title Request with Session ID: new-1 Successfully received and added to background task
2025-04-18 01:19:14,074 - api_logger - INFO - Starting background response generation for session_id: new-1, job_title: data scientist
2025-04-18 01:19:14,074 - api_logger - INFO - Checking if session does not exist for session_id: new-1, request_type: job_title
2025-04-18 01:19:14,330 - api_logger - INFO - Updating job experience for session_id: new-1
2025-04-18 01:19:15,144 - api_logger - INFO - Inserting status for session_id: new-1, request_type: job_title, status: Inprogress
2025-04-18 01:19:15,660 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-18 01:19:17,332 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 01:19:28,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-18 01:19:28,332 - api_logger - INFO - Response received successfully.
2025-04-18 01:19:28,334 - api_logger - INFO - Succesfully generated job title questions for session_id: new-1
2025-04-18 01:19:28,334 - api_logger - INFO - Inserting record for session_id: new-1, request_type: job_title
2025-04-18 01:19:28,848 - api_logger - INFO - Inserted initial status for session_id: new-1
2025-04-18 01:19:28,848 - api_logger - INFO - Updating status for session_id: new-1, request_type: job_title, status: complete
2025-04-18 01:19:29,352 - api_logger - INFO - Status updated successfully.
2025-04-18 01:19:29,352 - api_logger - INFO - Response generated for session_id: new-1, response_time: 14ms
2025-04-18 01:20:04,238 - api_logger - INFO - Generating feedback for question: What is your strength in coding
2025-04-18 01:20:04,260 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 01:20:08,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-18 01:20:08,524 - api_logger - INFO - Response received successfully.
2025-04-18 17:53:50,027 - api_logger - INFO - Fetching prompt for request type: RequestType.feedback
2025-04-18 17:53:50,027 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 17:53:56,851 - api_logger - INFO - Prompt retrieved successfully.
2025-04-18 17:56:41,804 - api_logger - INFO - Updating prompt for request type: RequestType.feedback
2025-04-18 17:56:41,804 - api_logger - ERROR - Validation failed: Missing placeholder(s): {suggestions}
2025-04-18 17:57:19,148 - api_logger - INFO - Updating prompt for request type: RequestType.feedback
2025-04-18 17:57:19,148 - api_logger - INFO - Updating prompt for request_type: RequestType.feedback
2025-04-18 17:57:21,645 - api_logger - INFO - Prompt updated successfully.
2025-04-18 17:57:25,614 - api_logger - INFO - Fetching prompt for request type: RequestType.feedback
2025-04-18 17:57:25,614 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 17:57:27,327 - api_logger - INFO - Prompt retrieved successfully.
2025-04-18 17:58:03,404 - api_logger - INFO - Updating prompt for request type: RequestType.feedback
2025-04-18 17:58:03,404 - api_logger - INFO - Updating prompt for request_type: RequestType.feedback
2025-04-18 17:58:05,604 - api_logger - INFO - Prompt updated successfully.
2025-04-18 17:58:40,843 - api_logger - INFO - Fetching prompt for request type: RequestType.feedback
2025-04-18 17:58:40,848 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 17:58:42,700 - api_logger - INFO - Prompt retrieved successfully.
2025-04-18 18:00:37,708 - api_logger - INFO - Generating feedback for question: What are your strengths
2025-04-18 18:00:37,708 - api_logger - INFO - Fetching prompt for request type: feedback
2025-04-18 18:00:37,708 - api_logger - ERROR - Error generating feedback: 'str' object has no attribute 'value'
2025-04-18 18:04:38,926 - api_logger - INFO - Generating feedback for question: What are your strengths
2025-04-18 18:04:38,926 - api_logger - INFO - Fetching prompt for request type: feedback
2025-04-18 18:04:38,926 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 18:04:40,743 - api_logger - INFO - Prompt retrieved successfully.
2025-04-18 18:04:40,961 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 18:04:41,391 - api_logger - ERROR - Error occurred: 2 validation errors for SystemMessage
content.str
  Input should be a valid string [type=string_type, input_value={'status': True, 'prompt'...ping the user's voice."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
content.list[union[str,dict[any,any]]]
  Input should be a valid list [type=list_type, input_value={'status': True, 'prompt'...ping the user's voice."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/list_type
2025-04-18 18:04:41,408 - api_logger - INFO - Retrying Again
2025-04-18 18:04:41,514 - api_logger - ERROR - Retry failed: 2 validation errors for SystemMessage
content.str
  Input should be a valid string [type=string_type, input_value={'status': True, 'prompt'...ping the user's voice."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
content.list[union[str,dict[any,any]]]
  Input should be a valid list [type=list_type, input_value={'status': True, 'prompt'...ping the user's voice."}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/list_type
2025-04-18 18:04:41,525 - api_logger - ERROR - Error generating feedback: Invalid response format from model
2025-04-18 18:11:14,189 - api_logger - INFO - Fetching prompt for request type: RequestType.feedback
2025-04-18 18:11:14,191 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 18:11:15,902 - api_logger - INFO - Prompt retrieved successfully.
2025-04-18 18:11:23,120 - api_logger - INFO - Generating feedback for question: What are your strengths
2025-04-18 18:11:23,122 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 18:11:25,340 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 18:11:31,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-18 18:11:31,714 - api_logger - INFO - Response received successfully.
2025-04-18 18:11:44,992 - api_logger - INFO - Fetching prompt for request type: RequestType.job_title
2025-04-18 18:11:44,992 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-18 18:11:46,972 - api_logger - INFO - Prompt retrieved successfully.
2025-04-18 18:12:12,441 - api_logger - INFO - Request received for Job Title with Session ID: ved-new Model: gpt-3.5-turbo and is_ca: False and Experience Level: None and Number of Questions: 10
2025-04-18 18:12:14,220 - api_logger - INFO - Job Title Request with Session ID: ved-new Successfully received and added to background task
2025-04-18 18:12:14,226 - api_logger - INFO - Starting background response generation for session_id: ved-new, job_title: Data scientist
2025-04-18 18:12:14,226 - api_logger - INFO - Checking if session does not exist for session_id: ved-new, request_type: job_title
2025-04-18 18:12:14,483 - api_logger - INFO - No previous session id
2025-04-18 18:12:14,484 - api_logger - INFO - Inserting status for session_id: ved-new, request_type: job_title, status: Inprogress
2025-04-18 18:12:15,015 - api_logger - INFO - Fetching latest prompt for request_type: job_title
2025-04-18 18:12:16,630 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 18:12:21,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-18 18:12:21,742 - api_logger - INFO - Response received successfully.
2025-04-18 18:12:21,742 - api_logger - INFO - Succesfully generated job title questions for session_id: ved-new
2025-04-18 18:12:21,742 - api_logger - INFO - Inserting record for session_id: ved-new, request_type: job_title
2025-04-18 18:12:22,251 - api_logger - INFO - Inserted initial status for session_id: ved-new
2025-04-18 18:12:22,257 - api_logger - INFO - Updating status for session_id: ved-new, request_type: job_title, status: complete
2025-04-18 18:12:22,770 - api_logger - INFO - Status updated successfully.
2025-04-18 18:12:22,770 - api_logger - INFO - Response generated for session_id: ved-new, response_time: 7ms
2025-04-18 18:12:32,800 - api_logger - INFO - Fetching session data for session ID: ved-new and is_ca: False
2025-04-18 18:12:34,549 - api_logger - INFO - Fetching session data for session_id: ved-new
2025-04-18 18:12:34,832 - api_logger - INFO - Session data fetched successfully for session ID: ved-new
2025-04-18 18:16:48,129 - api_logger - INFO - Request received for JD with Session ID: ved-new Model: gpt-3.5-turbo and is_ca: False
2025-04-18 18:16:49,770 - api_logger - INFO - JD Request with Session ID: ved-new Successfully received and added to background task
2025-04-18 18:16:49,791 - api_logger - INFO - Processing JD upload for session_id: ved-new
2025-04-18 18:16:49,794 - api_logger - INFO - Checking if session does not exist for session_id: ved-new, request_type: jd
2025-04-18 18:16:50,061 - api_logger - INFO - No previous session id
2025-04-18 18:16:50,061 - api_logger - INFO - Inserting status for session_id: ved-new, request_type: jd, status: Inprogress
2025-04-18 18:16:50,713 - api_logger - INFO - Fetching latest prompt for request_type: jd
2025-04-18 18:16:52,737 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 18:16:56,978 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-18 18:16:57,099 - api_logger - INFO - Response received successfully.
2025-04-18 18:16:57,099 - api_logger - INFO - Inserting record for session_id: ved-new, request_type: jd
2025-04-18 18:16:57,789 - api_logger - INFO - JD response generated for session_id: ved-new, response_time: 7.304696798324585s
2025-04-18 18:16:57,789 - api_logger - INFO - Updating status for session_id: ved-new, request_type: jd, status: complete
2025-04-18 18:16:58,327 - api_logger - INFO - Status updated successfully.
2025-04-18 18:17:04,789 - api_logger - INFO - Fetching session data for session ID: ved-new and is_ca: False
2025-04-18 18:17:06,748 - api_logger - INFO - Fetching session data for session_id: ved-new
2025-04-18 18:17:07,109 - api_logger - INFO - Session data fetched successfully for session ID: ved-new
2025-04-18 19:05:58,300 - api_logger - INFO - Generating feedback for question: Can you explain the difference between supervised and unsupervised learning?
2025-04-18 19:05:58,301 - api_logger - INFO - Fetching latest prompt for request_type: feedback
2025-04-18 19:06:00,626 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-04-18 19:06:05,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-04-18 19:06:06,090 - api_logger - INFO - Response received successfully.
2025-04-18 19:34:53,130 - api_logger - INFO - Updating prompt for request type: RequestType.feedback
2025-04-18 19:34:53,142 - api_logger - INFO - Updating prompt for request_type: RequestType.feedback
2025-04-18 19:34:55,227 - api_logger - INFO - Prompt updated successfully.
2025-04-18 19:35:27,248 - api_logger - INFO - Updating prompt for request type: RequestType.feedback
2025-04-18 19:35:27,252 - api_logger - INFO - Updating prompt for request_type: RequestType.feedback
2025-04-18 19:35:29,735 - api_logger - INFO - Prompt updated successfully.
