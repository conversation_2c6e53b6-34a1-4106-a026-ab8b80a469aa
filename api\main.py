from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from prometheus_fastapi_instrumentator import Instrumentator
from api.routers import interview_experience, prompt_management, question_generation,feedback
import uvicorn

from api.common.api_logger import api_logger as logger

app = FastAPI()
Instrumentator().instrument(app).expose(app)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow any origin
    allow_credentials=False,  # Must be False if allow_origins is set to wildcard
    allow_methods=["*"],  # Allow all methods (GET, POST, PUT, DELETE, etc.)
    allow_headers=["*"],  # Allow all headers
)

app.include_router(question_generation.router, tags=["Question Generation"])
app.include_router(interview_experience.router, tags=["Interview Experience"])
app.include_router(prompt_management.router, tags=["Prompt Management"])    
app.include_router(feedback.router,  tags=["Feedback"])  

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8001, reload=True)