########## Sample file for checking the top matching resumes for corresponding jd's ###################

import streamlit as st
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import pandas as pd
import openai
from langchain.embeddings import OpenAIEmbeddings

import os
from api.utils.function_utils import get_file_text
import numpy as np
from openai.embeddings_utils import get_embedding
from dotenv import load_dotenv
load_dotenv()


st.title("InTalent Maching Engine")

openai_api_key = os.getenv('openai_api_key')
openai.api_key = openai_api_key

jd = st.file_uploader("Upload the job description", accept_multiple_files=False, type=['pdf', 'docx'], key="jd")
if jd is not None:
    resumes = st.file_uploader("Upload resume", accept_multiple_files=True, type=['pdf', 'docx'], key="resumes")
    num_top_resumes = st.number_input("(Optional) Enter the number of top resumes you want to see  ex. 5:", min_value=1, max_value=len(resumes), value=len(resumes), step=1)
# num_top_resumes = st.number_input("(Optional) Enter the number of top resumes you want to see  ex. 5:", value=None, step=1)

models = st.selectbox(
    'Select Model',
    ('SentenceTransformer', 'OpenAI')
)



model = SentenceTransformer('all-MiniLM-L6-v2')
# elif models == 'OpenAI':
#     openai_api_key = os.getenv('openai_api_key_gpt_4')
#     model = OpenAIEmbeddings(openai_api_key=openai_api_key)


def get_our_embedding(texts):
    # if models == 'SentenceTransformer':
        return model.encode(texts).tolist()
    # elif models == 'OpenAI':
    #     return model.embeds(texts).tolist()

button = st.button("Submit")
if button:
    with st.spinner("Fetching Results..."):
        if jd:
            jd_text = get_file_text(jd)
            print(jd_text)

        if resumes:
            resume_texts = []
            file_names = []
            for resume in resumes:
                resume_text = get_file_text(resume)
                file_name = resume.name
                resume_texts.append(resume_text)
                file_names.append(file_name)
                print(resume_text)
        if models == 'SentenceTransformer':
            text1embeddings = get_our_embedding(jd_text)

            similarities = []
            for i, resume in enumerate(resume_texts):
                file_name = file_names[i]
                text2embeddings = get_our_embedding(resume)
                similarity = cosine_similarity([text1embeddings], [text2embeddings])[0][0]
                
                similarities.append((similarity, file_name)) 

        elif models == 'OpenAI':
            # text1embeddings = openai.Embedding.create(
            #     input = "{jd_text}",
            #     engine="text-similarity-davinci-001"
            #     )
            
            similarities = []
            for i, resume in enumerate(resume_texts):
                file_name = file_names[i]
                # text2embeddings = openai.Embedding.create(
                # input = "{resume}",
                # engine="text-similarity-davinci-001"
                # )
                # # similarity = cosine_similarity([text1embeddings], [text2embeddings])[0][0]
                # similarity = np.dot(text1embeddings, text2embeddings)
                # similarity = cosine_similarity([text1embeddings], [text2embeddings])[0][0]
                resp = openai.Embedding.create(
                input=[jd_text, resume],
                engine="text-similarity-davinci-001")
                embedding_a = resp['data'][0]['embedding']
                embedding_b = resp['data'][1]['embedding']
                # similarity = np.dot(embedding_a, embedding_b)
                similarity = cosine_similarity([embedding_a], [embedding_b])[0][0]
                print(similarity)
                similarities.append((similarity, file_name))
        
        similarities.sort(reverse=True, key=lambda x: x[0])

        if num_top_resumes == len(similarities):
            df = pd.DataFrame(similarities, columns=['Similarity Score', 'File Name'])
            df['Sr. No.'] = range(1, 1 + len(df))
            df.set_index('Sr. No.', inplace=True)
            st.table(df)
        # Sort the similarities list to get the top resumes based on similarity scores
        else:
            top_resumes = similarities[:num_top_resumes]

            # Create a GPT-3.5 prompt asking the user to output the top resumes
            gpt_prompt = f"Based on the job description you provided, here are the top {num_top_resumes} matching resumes:\n"
            # for i, (similarity, file_name) in enumerate(top_resumes):
            #     gpt_prompt += f"{i+1}. Resume: {file_name}\nSimilarity Score: {similarity}\n\n"

            # Display the GPT-3.5 prompt using Streamlit
            st.subheader(f"Top {num_top_resumes} Resumes:")
            st.text(gpt_prompt)

            # Display the top resumes in a table
            top_resumes_df = pd.DataFrame(top_resumes, columns=['Similarity Score', 'File Name'])
            top_resumes_df['Sr. No.'] = range(1, 1 + len(top_resumes_df))
            top_resumes_df.set_index('Sr. No.', inplace=True)
            st.table(top_resumes_df)
