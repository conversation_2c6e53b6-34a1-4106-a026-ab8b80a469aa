"""
This module provides functions to interact with session data in the database.
It includes functionalities to fetch session data, check if a session exists,
and retrieve requests associated with a session ID.
"""

from api.database.connection_db import get_connection
from api.common.api_logger import api_logger as logger

def fetch_session_data(session_id: str, connection):
    """
    Fetches session data for a given session ID.

    Parameters:
    - session_id (str): The session ID.
    - connection: The database connection object.

    Returns:
    - result: A list of dictionaries containing session data.
    """
    logger.info(f"Fetching session data for session_id: {session_id}")
    query = """
    SELECT * FROM requests
    WHERE session_id = %s
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute(query, (session_id,))
            result = cursor.fetchall()
            return result
    except Exception as e:
        logger.error(f"Error fetching session data: {e}")
        return None
    
def does_session_not_exist(session_id: str, request_type: str, connection) -> bool:
    """
    Checks if a session does not exist in the jobs table.

    Parameters:
    - session_id (str): The session ID.
    - request_type (str): The type of request.
    - connection: The database connection object.

    Returns:
    - bool: True if the session does not exist, False otherwise.
    """
    try:
        logger.info(f"Checking if session does not exist for session_id: {session_id}, request_type: {request_type}")
        with connection.cursor() as cursor:
            # Check if a row with the given session_id and request_type does not exist
            cursor.execute("SELECT 1 FROM jobs WHERE session_id = %s AND request_type = %s LIMIT 1",
                           (session_id, request_type))
            row_exists = cursor.fetchone() is not None
            return not row_exists  # Return True if row does not exist, False if it exists

    except Exception as e:
        logger.error("Exception during database query:", e)
        return False
    
def get_requests_by_session(session_id, connection):
    """
    Fetches all requests associated with a given session ID.

    Parameters:
    - session_id (str): The session ID.
    - connection: The database connection object.

    Returns:
    - result: A list of dictionaries containing request details.
    """
    try:
        logger.info(f"Fetching requests for session_id: {session_id}")
        # Establish a connection using the get_connection function
        with connection.cursor() as cursor:
            # SQL query to select records by session_id
            sql = "SELECT request_type, gpt_response FROM requests WHERE session_id = %s and request_type != 'interview_experience'"
            cursor.execute(sql, (session_id,))

            # Fetch all the matching records
            result = cursor.fetchall()
            return result
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        return []