# My Interview Practice

![image](https://github.com/PradipNichite/raj_resume/assets/83905457/841ca764-4f00-48b0-9313-a11896295830)

---

## Table of Contents
- [Project Overview](#project-overview)
  - [Phase 1: Role Type Input](#phase-1-role-type-input)
  - [Phase 2: Job Description Input](#phase-2-job-description-jd-input)
  - [Phase 3: Resume Input](#phase-3-resume-input)
  - [Generate Questions](#after-gathering-all-the-context)
- [Features](#features)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
- [Usage](#usage)
  - [API Usage](#api-usage)
  - [Streamlit Apps](#streamlit-apps)
- [API Endpoints](#api-endpoints)
---

## Project-Overview
This project leverages a large language model (LLM) to generate interview questions based on provided context. The process is divided into three phases:

### Phase 1: Role Type Input
* Define the type of interview role.

### Phase 2: Job Description (JD) Input
* Provide the job description.

### Phase 3: Resume Input
* Input the candidate's resume.

### After gathering all the context
* The model generates tailored interview questions.

---

## Features
* Context-aware question generation
* Multi-phase input process
* Customizable and extensible for different interview roles

---

## Project-Structure
```bash
ai/
├── api/                    # FastAPI backend files
│   ├── common/             # Common utilities and dependencies
│   │   ├── api_logger.py   # Logger setup for API requests
│   │   ├── dependencies.py  # API key authentication dependencies
│   │   └── pydantic_models.py # Pydantic models for data validation
│   ├── database/           # Database interaction modules
│   │   ├── connection_db.py # Database connection management
│   │   ├── job_requests_db.py # Functions for job requests related db task
│   │   ├── job_status_db.py  # Functions to manage job statuses related db task
│   │   ├── prompt_db.py      # Functions to interact with the prompt related db task
│   │   └── session_db.py     # Functions to manage session data related db task
│   ├── prompts/            # Functions to create prompts for question generation
│   │   └── prompts.py       # Initial message prompts for generating questions
│   ├── routers/            # API route definitions
│   │   ├── interview_experience.py # Endpoints for interview experience
│   │   ├── prompt_management.py     # Endpoints for managing prompts
│   │   └── question_generation.py    # Endpoints for question generation
│   ├── utils/              # Utility functions for processing
│   │   ├── function_utils.py # File handling and text extraction utilities
│   │   └── processing_functions.py # Main processing functions for API tasks
│   └── main.py             # Main entry point for FastAPI (collection of routers)
├── app/                    # Streamlit applications
│   ├── streamlit.py        # Generate sample questions from JD and Resume
│   └── update_prompt_st.py  # Update JD and resume prompts
├── logs/                   # Contains API logs as per dates in separate directory inside
├── legacy_files/           # Have legacy files not in use
├── temp/                   # Used for temporarily storing the uploaded jd and resume files
├── scripts/                # Contains scripts to run for backup of data and running api on vm
├── test/                   # Contains files, which can be used to test code
├── requirements.txt        # List of minimal required packages to run code
├── full_requirements.txt   # List of required packages to run code and development
├── .env.example            # Example .env file which needs to be made with required keys 
└── README.md               # Project documentation
```

## Getting-Started

### Prerequisites
* Python 3.8+
* `pip` for package management
* Virtual environment (recommended)

---

### Installation

#### 1. Clone the Repository
```bash
git clone https://github.com/myinterviewpractice1/ai
cd ai
```

#### 2. Create and Activate Virtual Environment
Windows
```bash
python -m venv venv
venv\Scripts\activate
```
Mac/Linux

```bash
python3 -m venv venv
source venv/bin/activate
```

#### 3. Install Required Packages

```bash
pip install -r requirements.txt
```

## Usage
### API Usage

Run from the `ai` directory:

#### 1. Start the FastAPI Server
```bash

uvicorn api.main:app --reload
```

### Streamlit Apps
#### 1. Generate Questions from JD and Resume

Run from the `ai` directory:

```bash
streamlit run app/streamlit.py
```

#### 2. Update Prompts for JD and Resume

```bash
streamlit run app/update_prompt_st.py
```

## API Endpoints

### 1. `/upload_job_title_experience_level/`
- **Purpose**: Uploads job title, experience level, and number of questions for processing in the background.
- **Input**:
  - `session_id` (str): The session identifier.
  - `job_title` (str): The job title.
  - `number_of_questions` (int): Number of questions to generate.
  - `model` (GPTModels): The model to use for processing.
  - `experience_level` (str, optional): The experience level.
  - `is_ca` (bool, optional): Indicates if the connection is for a Canadian (default is False).
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns a message indicating the request is being processed in the background.

Sample response:
```json
{
  "status": true,
  "message": "Request received, processing in the background"
}
```

### 2. `/upload_jd/`
- **Purpose**: Uploads a job description (JD) for processing in the background.
- **Input**:
  - `session_id` (str): The session identifier.
  - `jd_text` (str, optional): The job description text.
  - `jd` (UploadFile, optional): The job description file (.docx/.pdf).
  - `model` (GPTModels): The model to use for processing.
  - `is_ca` (bool, optional): Indicates if the connection is for a Canadian (default is False).
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns a message indicating the request is being processed in the background.

Sample response:
```json
{
  "status": true,
  "message": "Request received, processing in the background"
}
```

### 3. `/upload_resume/`
- **Purpose**: Uploads a resume for processing in the background.
- **Input**:
  - `session_id` (str): The session identifier.
  - `resume` (UploadFile): The resume file.
  - `model` (GPTModels): The model to use for processing.
  - `is_ca` (bool, optional): Indicates if the connection is for a Canadian (default is False).
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns a message indicating the request is being processed in the background.

Sample response:
```json
{
  "status": true,
  "message": "Request received, processing in the background"
}
```

### 4. `/get_interview_experience/`
- **Purpose**: Fetches interview experience based on session ID and model.
- **Input**:
  - `session_id` (str): The session identifier.
  - `model` (GPTModels): The model to use for processing.
  - `is_ca` (bool, optional): Indicates if the connection is for a Canadian (default is False).
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns the processed interview experience or an error message.

Sample Response: 
```json
{
  "session_id": "testing",
  "questions": [
    "Can you please introduce yourself and tell me a little about your background?",
    "What programming languages and tools do you commonly use for data analysis, and why do you prefer them?",
  ],
  "status": true
}
```

### 5. `/get_session_data/{session_id}`
- **Purpose**: Retrieves session data for a given session ID.
- **Input**:
  - `session_id` (str): The session identifier.
  - `is_ca` (bool, optional): Indicates if the connection is for a Canadian (default is False).
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns a list of session data or an error message if the session is not found.

Sample Response:
```json
[
  {
    "id": 28163,
    "session_id": "testing",
    "request_type": "interview_experience",
    "role": null,
    "gpt_response": "{\n  \"questions\": [\n    \"Tell us about yourself.\",\n    \"Your resume mentioned that you have experience in Python. How have you utilized this skill in your previous roles?\"}",
    "created_at": "2025-03-23T18:08:13",
    "updated_at": "2025-03-23T18:08:13",
    "response_time": 6,
    "experience_level": null,
    "model": "gpt-4o-mini",
    "total_tokens": "1074",
    "number_of_questions": null,
    "job_description": null,
    "resume_text": null
  }
]
```

### 6. `/get_prompt/`
- **Purpose**: Retrieves the latest prompt for a specified request type.
- **Input**:
  - `request_type` (RequestType): The type of request (e.g., job_title, jd, resume, interview_experience).
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns the latest prompt or an error message if not found.

Sample Response:
```json
{
  "status": true,
  "prompt": "<Prompt Displayed here>"
}
```

### 7. `/update_prompt/`
- **Purpose**: Updates the prompt for a specified request type after validating placeholders.
- **Input**:
  - `request_type` (RequestType): The type of request.
  - `prompt` (str): The new prompt to update.
  - **Header**:
    - `api_key` (str): API key for authentication.
    - `api_secret` (str): API Secret key for authentication.
- **Output**: Returns a success message or an error message if validation fails.

Sample Response:
```json
{
  "status": true,
  "message": "Prompt updated successfully"
}
```